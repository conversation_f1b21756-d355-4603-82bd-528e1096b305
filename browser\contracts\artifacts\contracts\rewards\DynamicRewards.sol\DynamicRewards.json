{"_format": "hh-sol-artifact-1", "contractName": "DynamicRewards", "sourceName": "contracts/rewards/DynamicRewards.sol", "abi": [{"inputs": [{"internalType": "address", "name": "_stakingToken", "type": "address"}, {"internalType": "address", "name": "admin", "type": "address"}], "stateMutability": "nonpayable", "type": "constructor"}, {"anonymous": false, "inputs": [{"indexed": false, "internalType": "uint256", "name": "newPeriod", "type": "uint256"}], "name": "MinStakingPeriodUpdated", "type": "event"}, {"anonymous": false, "inputs": [{"indexed": true, "internalType": "address", "name": "user", "type": "address"}, {"indexed": false, "internalType": "uint256", "name": "amount", "type": "uint256"}, {"indexed": false, "internalType": "address", "name": "token", "type": "address"}], "name": "<PERSON><PERSON><PERSON>laimed", "type": "event"}, {"anonymous": false, "inputs": [{"indexed": false, "internalType": "uint256", "name": "scheduleId", "type": "uint256"}], "name": "RewardScheduleAdded", "type": "event"}, {"anonymous": false, "inputs": [{"indexed": true, "internalType": "bytes32", "name": "role", "type": "bytes32"}, {"indexed": true, "internalType": "bytes32", "name": "previousAdminRole", "type": "bytes32"}, {"indexed": true, "internalType": "bytes32", "name": "newAdminRole", "type": "bytes32"}], "name": "RoleAdminChanged", "type": "event"}, {"anonymous": false, "inputs": [{"indexed": true, "internalType": "bytes32", "name": "role", "type": "bytes32"}, {"indexed": true, "internalType": "address", "name": "account", "type": "address"}, {"indexed": true, "internalType": "address", "name": "sender", "type": "address"}], "name": "RoleGranted", "type": "event"}, {"anonymous": false, "inputs": [{"indexed": true, "internalType": "bytes32", "name": "role", "type": "bytes32"}, {"indexed": true, "internalType": "address", "name": "account", "type": "address"}, {"indexed": true, "internalType": "address", "name": "sender", "type": "address"}], "name": "RoleRevoked", "type": "event"}, {"anonymous": false, "inputs": [{"indexed": true, "internalType": "address", "name": "user", "type": "address"}, {"indexed": false, "internalType": "uint256", "name": "amount", "type": "uint256"}], "name": "Staked", "type": "event"}, {"anonymous": false, "inputs": [{"indexed": true, "internalType": "address", "name": "user", "type": "address"}, {"indexed": false, "internalType": "uint256", "name": "amount", "type": "uint256"}], "name": "Withdrawn", "type": "event"}, {"inputs": [], "name": "DEFAULT_ADMIN_ROLE", "outputs": [{"internalType": "bytes32", "name": "", "type": "bytes32"}], "stateMutability": "view", "type": "function"}, {"inputs": [], "name": "MIN_STAKING_PERIOD", "outputs": [{"internalType": "uint256", "name": "", "type": "uint256"}], "stateMutability": "view", "type": "function"}, {"inputs": [], "name": "MULTIPLIER", "outputs": [{"internalType": "uint256", "name": "", "type": "uint256"}], "stateMutability": "view", "type": "function"}, {"inputs": [], "name": "PERCENTAGE_BASE", "outputs": [{"internalType": "uint256", "name": "", "type": "uint256"}], "stateMutability": "view", "type": "function"}, {"inputs": [], "name": "REWARD_MANAGER", "outputs": [{"internalType": "bytes32", "name": "", "type": "bytes32"}], "stateMutability": "view", "type": "function"}, {"inputs": [], "name": "TOKEN_UNIT", "outputs": [{"internalType": "uint256", "name": "", "type": "uint256"}], "stateMutability": "view", "type": "function"}, {"inputs": [{"internalType": "uint256", "name": "startTime", "type": "uint256"}, {"internalType": "uint256", "name": "duration", "type": "uint256"}, {"internalType": "uint256", "name": "totalReward", "type": "uint256"}, {"internalType": "address", "name": "rewardsToken", "type": "address"}], "name": "addRewardSchedule", "outputs": [], "stateMutability": "nonpayable", "type": "function"}, {"inputs": [{"internalType": "address", "name": "account", "type": "address"}], "name": "balanceOf", "outputs": [{"internalType": "uint256", "name": "", "type": "uint256"}], "stateMutability": "view", "type": "function"}, {"inputs": [], "name": "claimRewards", "outputs": [], "stateMutability": "nonpayable", "type": "function"}, {"inputs": [], "name": "currentScheduleId", "outputs": [{"internalType": "uint256", "name": "", "type": "uint256"}], "stateMutability": "view", "type": "function"}, {"inputs": [{"internalType": "address", "name": "account", "type": "address"}], "name": "earned", "outputs": [{"internalType": "uint256", "name": "total", "type": "uint256"}], "stateMutability": "view", "type": "function"}, {"inputs": [], "name": "getActiveSchedules", "outputs": [{"internalType": "uint256[]", "name": "", "type": "uint256[]"}], "stateMutability": "view", "type": "function"}, {"inputs": [{"internalType": "bytes32", "name": "role", "type": "bytes32"}], "name": "getRoleAdmin", "outputs": [{"internalType": "bytes32", "name": "", "type": "bytes32"}], "stateMutability": "view", "type": "function"}, {"inputs": [{"internalType": "bytes32", "name": "role", "type": "bytes32"}, {"internalType": "address", "name": "account", "type": "address"}], "name": "grantRole", "outputs": [], "stateMutability": "nonpayable", "type": "function"}, {"inputs": [{"internalType": "bytes32", "name": "role", "type": "bytes32"}, {"internalType": "address", "name": "account", "type": "address"}], "name": "hasRole", "outputs": [{"internalType": "bool", "name": "", "type": "bool"}], "stateMutability": "view", "type": "function"}, {"inputs": [], "name": "lastUpdateTime", "outputs": [{"internalType": "uint256", "name": "", "type": "uint256"}], "stateMutability": "view", "type": "function"}, {"inputs": [], "name": "minStakingPeriod", "outputs": [{"internalType": "uint256", "name": "", "type": "uint256"}], "stateMutability": "view", "type": "function"}, {"inputs": [], "name": "pause", "outputs": [], "stateMutability": "nonpayable", "type": "function"}, {"inputs": [{"internalType": "bytes32", "name": "role", "type": "bytes32"}, {"internalType": "address", "name": "account", "type": "address"}], "name": "renounceRole", "outputs": [], "stateMutability": "nonpayable", "type": "function"}, {"inputs": [{"internalType": "bytes32", "name": "role", "type": "bytes32"}, {"internalType": "address", "name": "account", "type": "address"}], "name": "revokeRole", "outputs": [], "stateMutability": "nonpayable", "type": "function"}, {"inputs": [], "name": "rewardPerToken", "outputs": [{"internalType": "uint256", "name": "", "type": "uint256"}], "stateMutability": "view", "type": "function"}, {"inputs": [], "name": "rewardPerTokenStored", "outputs": [{"internalType": "uint256", "name": "", "type": "uint256"}], "stateMutability": "view", "type": "function"}, {"inputs": [{"internalType": "uint256", "name": "", "type": "uint256"}], "name": "rewardSchedules", "outputs": [{"internalType": "uint256", "name": "startTime", "type": "uint256"}, {"internalType": "uint256", "name": "endTime", "type": "uint256"}, {"internalType": "uint256", "name": "totalRewards", "type": "uint256"}, {"internalType": "uint256", "name": "claimedRewards", "type": "uint256"}, {"internalType": "address", "name": "rewardsToken", "type": "address"}], "stateMutability": "view", "type": "function"}, {"inputs": [{"internalType": "address", "name": "", "type": "address"}], "name": "rewards", "outputs": [{"internalType": "uint256", "name": "", "type": "uint256"}], "stateMutability": "view", "type": "function"}, {"inputs": [{"internalType": "uint256", "name": "period", "type": "uint256"}], "name": "setMinStakingPeriod", "outputs": [], "stateMutability": "nonpayable", "type": "function"}, {"inputs": [{"internalType": "uint256", "name": "amount", "type": "uint256"}], "name": "stake", "outputs": [], "stateMutability": "nonpayable", "type": "function"}, {"inputs": [], "name": "stakingToken", "outputs": [{"internalType": "contract IERC20", "name": "", "type": "address"}], "stateMutability": "view", "type": "function"}, {"inputs": [{"internalType": "bytes4", "name": "interfaceId", "type": "bytes4"}], "name": "supportsInterface", "outputs": [{"internalType": "bool", "name": "", "type": "bool"}], "stateMutability": "view", "type": "function"}, {"inputs": [], "name": "totalStaked", "outputs": [{"internalType": "uint256", "name": "", "type": "uint256"}], "stateMutability": "view", "type": "function"}, {"inputs": [], "name": "unpause", "outputs": [], "stateMutability": "nonpayable", "type": "function"}, {"inputs": [{"internalType": "address", "name": "", "type": "address"}], "name": "userRewardPerTokenPaid", "outputs": [{"internalType": "uint256", "name": "", "type": "uint256"}], "stateMutability": "view", "type": "function"}, {"inputs": [{"internalType": "uint256", "name": "amount", "type": "uint256"}], "name": "withdraw", "outputs": [], "stateMutability": "nonpayable", "type": "function"}], "bytecode": "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", "deployedBytecode": "0x608060405234801561001057600080fd5b506004361061017f5760003560e01c80628cc2621461018457806301ffc9a7146101aa578063059f8b16146101cd578063069c34f6146101dc5780630700037d146101e55780630b35a4b0146102055780631c1121c51461021a578063248a9ca3146102915780632e1a7d4d146102a45780632f2ff15d146102b957806336568abe146102cc578063372500ab146102df5780633f4ba83a146102e75780635932fdba146102ef57806370a082311461030457806372f702f31461032d578063817b1cd2146103615780638456cb591461036957806385ecdd931461037157806387c139431461037a57806388ee6a43146103835780638b8763471461039657806391d14854146103b6578063a217fddf146103c9578063a5c9cd82146101cd578063a694fc3a146103d1578063c743dabb146103e4578063c8f33c91146103ee578063cd3daf9d146103f7578063d547741f146103ff578063df136d6514610412578063e075d82c1461041b575b600080fd5b6101976101923660046117b8565b61042e565b6040519081526020015b60405180910390f35b6101bd6101b83660046117d3565b61046a565b60405190151581526020016101a1565b610197670de0b6b3a764000081565b61019760075481565b6101976101f33660046117b8565b600d6020526000908152604090205481565b61020d6104a1565b6040516101a191906117fd565b610260610228366004611841565b60086020526000908152604090208054600182015460028301546003840154600490940154929391929091906001600160a01b031685565b6040805195865260208601949094529284019190915260608301526001600160a01b0316608082015260a0016101a1565b61019761029f366004611841565b61059e565b6102b76102b2366004611841565b6105b3565b005b6102b76102c736600461185a565b61087c565b6102b76102da36600461185a565b61089d565b6102b761091b565b6102b7610aeb565b610197600080516020611b4683398151915281565b6101976103123660046117b8565b6001600160a01b031660009081526004602052604090205490565b6103547f000000000000000000000000000000000000000000000000000000000000000081565b6040516101a19190611886565b600354610197565b6102b7610b03565b61019760095481565b61019761271081565b6102b7610391366004611841565b610b1e565b6101976103a43660046117b8565b600e6020526000908152604090205481565b6101bd6103c436600461185a565b610b72565b610197600081565b6102b76103df366004611841565b610b9b565b61019762093a8081565b610197600b5481565b610197610d8d565b6102b761040d36600461185a565b610ded565b610197600c5481565b6102b761042936600461189a565b610e09565b600060015b6009548111610464576104468382611090565b61045090836118ef565b91508061045c81611902565b915050610433565b50919050565b60006001600160e01b03198216637965db0b60e01b148061049b57506301ffc9a760e01b6001600160e01b03198316145b92915050565b6060600060015b60095481116104ea576000818152600860205260409020600101544210156104d857816104d481611902565b9250505b806104e281611902565b9150506104a8565b506000816001600160401b038111156105055761050561191b565b60405190808252806020026020018201604052801561052e578160200160208202803683370190505b509050600060015b6009548111610595576000818152600860205260409020600101544210156105835780838361056481611902565b94508151811061057657610576611931565b6020026020010181815250505b8061058d81611902565b915050610536565b50909392505050565b60009081526020819052604090206001015490565b6105bb61125b565b60025460ff16156105e75760405162461bcd60e51b81526004016105de90611947565b60405180910390fd5b3360006105f2610d8d565b600c81905542600b5590506001600160a01b03821615610650576106158261042e565b6001600160a01b0383166000908152600d6020908152604080832093909355600e815282822084905560048152828220546005909152919020555b6007543360009081526006602052604090205461066d91906118ef565b4210156106bc5760405162461bcd60e51b815260206004820152601e60248201527f4d696e696d756d207374616b696e6720706572696f64206e6f74206d6574000060448201526064016105de565b600083116106dc5760405162461bcd60e51b81526004016105de90611973565b336000908152600460205260409020548311156107325760405162461bcd60e51b8152602060048201526014602482015273496e73756666696369656e742062616c616e636560601b60448201526064016105de565b61073b336112b4565b82600354610749919061199f565b600355336000908152600460208181526040808420546005835293208390555261077490849061199f565b33600081815260046020819052604080832094909455925163a9059cbb60e01b815290927f00000000000000000000000000000000000000000000000000000000000000006001600160a01b03169263a9059cbb926107d5928991016119b2565b6020604051808303816000875af11580156107f4573d6000803e3d6000fd5b505050506040513d601f19601f8201168201806040525081019061081891906119cb565b9050806108375760405162461bcd60e51b81526004016105de906119ed565b60405184815233907f7084f5476618d8e60b11ef0d7d3f06914655adb8793e28ff7f018d4c76d505d5906020015b60405180910390a250505061087960018055565b50565b6108858261059e565b61088e81611348565b6108988383611352565b505050565b6001600160a01b038116331461090d5760405162461bcd60e51b815260206004820152602f60248201527f416363657373436f6e74726f6c3a2063616e206f6e6c792072656e6f756e636560448201526e103937b632b9903337b91039b2b63360891b60648201526084016105de565b61091782826113d6565b5050565b61092361125b565b60025460ff16156109465760405162461bcd60e51b81526004016105de90611947565b61094f336112b4565b600060015b6009548111610a3e576000818152600860209081526040808320338452600a83528184208585529092528220549091819003610991575050610a2c565b600081116109d45760405162461bcd60e51b815260206004820152601060248201526f4e6f7468696e6720746f20636c61696d60801b60448201526064016105de565b336000908152600a6020908152604080832086845290915281205560038201546109ff9082906118ef565b60038301556004820154610a1d906001600160a01b0316338361143b565b610a2781856118ef565b935050505b80610a3681611902565b915050610954565b5060008111610a7c5760405162461bcd60e51b815260206004820152600a6024820152694e6f207265776172647360b01b60448201526064016105de565b604080518281527f00000000000000000000000000000000000000000000000000000000000000006001600160a01b0316602082015233917ff80b6d248ca65e589d3f24c7ce36e2df22ba16ba4e7656aad67e114abbe971d2910160405180910390a250610ae960018055565b565b6000610af681611348565b506002805460ff19169055565b6000610b0e81611348565b506002805460ff19166001179055565b600080516020611b46833981519152610b3681611348565b60078290556040518281527f8114637a99d6dbde6e7e5125969a8ecbe5d143eedc597560c9c79a8cb9a6b5d09060200160405180910390a15050565b6000918252602082815260408084206001600160a01b0393909316845291905290205460ff1690565b610ba361125b565b60025460ff1615610bc65760405162461bcd60e51b81526004016105de90611947565b336000610bd1610d8d565b600c81905542600b5590506001600160a01b03821615610c2f57610bf48261042e565b6001600160a01b0383166000908152600d6020908152604080832093909355600e815282822084905560048152828220546005909152919020555b60008311610c4f5760405162461bcd60e51b81526004016105de90611973565b610c58336112b4565b82600354610c6691906118ef565b60035533600090815260046020526040902054610c849084906118ef565b336000818152600460208181526040808420959095556006905283822042905592516323b872dd60e01b81529283019190915230602483015260448201859052906001600160a01b037f000000000000000000000000000000000000000000000000000000000000000016906323b872dd906064016020604051808303816000875af1158015610d18573d6000803e3d6000fd5b505050506040513d601f19601f82011682018060405250810190610d3c91906119cb565b905080610d5b5760405162461bcd60e51b81526004016105de906119ed565b60405184815233907f9e71bc8eea02a63969f509818f2dafb9254532904319f9dbda79b67bd34a5f3d90602001610865565b6000600354600003610da05750600c5490565b600354600c54670de0b6b3a7640000600b5442610dbd919061199f565b610dc79190611a16565b610dd19190611a16565b610ddb9190611a2d565b600c54610de891906118ef565b905090565b610df68261059e565b610dff81611348565b61089883836113d6565b600080516020611b46833981519152610e2181611348565b42851015610e665760405162461bcd60e51b815260206004820152601260248201527114dd185c9d081d1a5b59481a5b881c185cdd60721b60448201526064016105de565b60008411610ea95760405162461bcd60e51b815260206004820152601060248201526f24b73b30b634b210323ab930ba34b7b760811b60448201526064016105de565b60008311610ef15760405162461bcd60e51b8152602060048201526015602482015274125b9d985b1a59081c995dd85c9908185b5bdd5b9d605a1b60448201526064016105de565b6040516370a0823160e01b81526000906001600160a01b038416906370a0823190610f20903090600401611886565b602060405180830381865afa158015610f3d573d6000803e3d6000fd5b505050506040513d601f19601f82011682018060405250810190610f619190611a4f565b905083811015610fb05760405162461bcd60e51b815260206004820152601a602482015279496e73756666696369656e742072657761726420746f6b656e7360301b60448201526064016105de565b6000600960008154610fc190611902565b91905081905590506040518060a001604052808881526020018789610fe691906118ef565b81526020808201889052600060408084018290526001600160a01b03898116606095860152868352600884529181902085518155858401516001820155858201516002820155938501516003850155608090940151600490930180546001600160a01b031916939091169290921790915590518281527f85396969f10b93e89b84ce058bbce90a809c86067bb94a5f0243dfe9f9868943910160405180910390a150505050505050565b60008181526008602090815260408083206001600160a01b0386168452600590925282205415806110c15750805442105b156110d057600091505061049b565b805460018201546000916110e39161199f565b9050600081116111315760405162461bcd60e51b815260206004820152601960248201527824b73b30b634b21039b1b432b23ab63290323ab930ba34b7b760391b60448201526064016105de565b8154600090611140904261199f565b90508181111561114d5750805b670de0b6b3a7640000600083826111648186611a16565b61116e9190611a16565b6111789190611a2d565b90506000848385886002015461118e9190611a16565b6111989190611a16565b6111a29190611a2d565b90506000836003546111b49190611a16565b6001600160a01b038b166000908152600460205260409020546111d8908590611a16565b6111e29190611a2d565b905060006111f885670de0b6b3a7640000611a16565b6112028385611a16565b61120c9190611a2d565b6001600160a01b038c166000908152600a602090815260408083208e845290915290205490915080821161124157600061124b565b61124b818361199f565b9c9b505050505050505050505050565b6002600154036112ad5760405162461bcd60e51b815260206004820152601f60248201527f5265656e7472616e637947756172643a207265656e7472616e742063616c6c0060448201526064016105de565b6002600155565b60015b600954811161091757600081815260086020526040902060010154421015611336576112e38282611090565b6001600160a01b0383166000908152600a6020908152604080832085845290915290205461131191906118ef565b6001600160a01b0383166000908152600a602090815260408083208584529091529020555b8061134081611902565b9150506112b7565b610879813361158f565b61135c8282610b72565b610917576000828152602081815260408083206001600160a01b03851684529091529020805460ff191660011790556113923390565b6001600160a01b0316816001600160a01b0316837f2f8788117e7eff1d82e926ec794901d17c78024a50270940304540a733656f0d60405160405180910390a45050565b6113e08282610b72565b15610917576000828152602081815260408083206001600160a01b0385168085529252808320805460ff1916905551339285917ff6391f5c32d9c69d2a47ea670b442974b53935d1edc7fd64eb21e047a839171b9190a45050565b6040516370a0823160e01b815281906001600160a01b038516906370a0823190611469903090600401611886565b602060405180830381865afa158015611486573d6000803e3d6000fd5b505050506040513d601f19601f820116820180604052508101906114aa9190611a4f565b10156114f65760405162461bcd60e51b815260206004820152601b60248201527a496e73756666696369656e74207265776172642062616c616e636560281b60448201526064016105de565b60405163a9059cbb60e01b81526000906001600160a01b0385169063a9059cbb9061152790869086906004016119b2565b6020604051808303816000875af1158015611546573d6000803e3d6000fd5b505050506040513d601f19601f8201168201806040525081019061156a91906119cb565b9050806115895760405162461bcd60e51b81526004016105de906119ed565b50505050565b6115998282610b72565b610917576115a6816115e8565b6115b18360206115fa565b6040516020016115c2929190611a8c565b60408051601f198184030181529082905262461bcd60e51b82526105de91600401611afb565b606061049b6001600160a01b03831660145b60606000611609836002611a16565b6116149060026118ef565b6001600160401b0381111561162b5761162b61191b565b6040519080825280601f01601f191660200182016040528015611655576020820181803683370190505b509050600360fc1b8160008151811061167057611670611931565b60200101906001600160f81b031916908160001a905350600f60fb1b8160018151811061169f5761169f611931565b60200101906001600160f81b031916908160001a90535060006116c3846002611a16565b6116ce9060016118ef565b90505b6001811115611746576f181899199a1a9b1b9c1cb0b131b232b360811b85600f166010811061170257611702611931565b1a60f81b82828151811061171857611718611931565b60200101906001600160f81b031916908160001a90535060049490941c9361173f81611b2e565b90506116d1565b5083156117955760405162461bcd60e51b815260206004820181905260248201527f537472696e67733a20686578206c656e67746820696e73756666696369656e7460448201526064016105de565b9392505050565b80356001600160a01b03811681146117b357600080fd5b919050565b6000602082840312156117ca57600080fd5b6117958261179c565b6000602082840312156117e557600080fd5b81356001600160e01b03198116811461179557600080fd5b6020808252825182820181905260009190848201906040850190845b8181101561183557835183529284019291840191600101611819565b50909695505050505050565b60006020828403121561185357600080fd5b5035919050565b6000806040838503121561186d57600080fd5b8235915061187d6020840161179c565b90509250929050565b6001600160a01b0391909116815260200190565b600080600080608085870312156118b057600080fd5b8435935060208501359250604085013591506118ce6060860161179c565b905092959194509250565b634e487b7160e01b600052601160045260246000fd5b8082018082111561049b5761049b6118d9565b600060018201611914576119146118d9565b5060010190565b634e487b7160e01b600052604160045260246000fd5b634e487b7160e01b600052603260045260246000fd5b60208082526012908201527110dbdb9d1c9858dd081a5cc81c185d5cd95960721b604082015260600190565b6020808252601290820152710416d6f756e74206d757374206265203e20360741b604082015260600190565b8181038181111561049b5761049b6118d9565b6001600160a01b03929092168252602082015260400190565b6000602082840312156119dd57600080fd5b8151801515811461179557600080fd5b6020808252600f908201526e151c985b9cd9995c8819985a5b1959608a1b604082015260600190565b808202811582820484141761049b5761049b6118d9565b600082611a4a57634e487b7160e01b600052601260045260246000fd5b500490565b600060208284031215611a6157600080fd5b5051919050565b60005b83811015611a83578181015183820152602001611a6b565b50506000910152565b76020b1b1b2b9b9a1b7b73a3937b61d1030b1b1b7bab73a1604d1b815260008351611abe816017850160208801611a68565b7001034b99036b4b9b9b4b733903937b6329607d1b6017918401918201528351611aef816028840160208801611a68565b01602801949350505050565b6020815260008251806020840152611b1a816040850160208701611a68565b601f01601f19169190910160400192915050565b600081611b3d57611b3d6118d9565b50600019019056fe6393fa734211156fcc8c7dda6b0650f5c731d4c838c1db43445a149aa2f5b6fea26469706673582212204b64b831d1cc3d4b659db6270b58b36c47dbd5c7c3660150f54782fa9a448f9b64736f6c63430008140033", "linkReferences": {}, "deployedLinkReferences": {}}