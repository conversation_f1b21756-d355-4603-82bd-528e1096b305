{"_format": "hh-sol-artifact-1", "contractName": "DynamicRewards", "sourceName": "contracts/rewards/DynamicRewards.sol", "abi": [{"inputs": [{"internalType": "address", "name": "_stakingToken", "type": "address"}, {"internalType": "address", "name": "admin", "type": "address"}], "stateMutability": "nonpayable", "type": "constructor"}, {"anonymous": false, "inputs": [{"indexed": false, "internalType": "uint256", "name": "newPeriod", "type": "uint256"}], "name": "MinStakingPeriodUpdated", "type": "event"}, {"anonymous": false, "inputs": [{"indexed": true, "internalType": "address", "name": "user", "type": "address"}, {"indexed": false, "internalType": "uint256", "name": "amount", "type": "uint256"}, {"indexed": false, "internalType": "address", "name": "token", "type": "address"}], "name": "<PERSON><PERSON><PERSON>laimed", "type": "event"}, {"anonymous": false, "inputs": [{"indexed": false, "internalType": "uint256", "name": "scheduleId", "type": "uint256"}], "name": "RewardScheduleAdded", "type": "event"}, {"anonymous": false, "inputs": [{"indexed": true, "internalType": "bytes32", "name": "role", "type": "bytes32"}, {"indexed": true, "internalType": "bytes32", "name": "previousAdminRole", "type": "bytes32"}, {"indexed": true, "internalType": "bytes32", "name": "newAdminRole", "type": "bytes32"}], "name": "RoleAdminChanged", "type": "event"}, {"anonymous": false, "inputs": [{"indexed": true, "internalType": "bytes32", "name": "role", "type": "bytes32"}, {"indexed": true, "internalType": "address", "name": "account", "type": "address"}, {"indexed": true, "internalType": "address", "name": "sender", "type": "address"}], "name": "RoleGranted", "type": "event"}, {"anonymous": false, "inputs": [{"indexed": true, "internalType": "bytes32", "name": "role", "type": "bytes32"}, {"indexed": true, "internalType": "address", "name": "account", "type": "address"}, {"indexed": true, "internalType": "address", "name": "sender", "type": "address"}], "name": "RoleRevoked", "type": "event"}, {"anonymous": false, "inputs": [{"indexed": true, "internalType": "address", "name": "user", "type": "address"}, {"indexed": false, "internalType": "uint256", "name": "amount", "type": "uint256"}], "name": "Staked", "type": "event"}, {"anonymous": false, "inputs": [{"indexed": true, "internalType": "address", "name": "user", "type": "address"}, {"indexed": false, "internalType": "uint256", "name": "amount", "type": "uint256"}], "name": "Withdrawn", "type": "event"}, {"inputs": [], "name": "DEFAULT_ADMIN_ROLE", "outputs": [{"internalType": "bytes32", "name": "", "type": "bytes32"}], "stateMutability": "view", "type": "function"}, {"inputs": [], "name": "MIN_STAKING_PERIOD", "outputs": [{"internalType": "uint256", "name": "", "type": "uint256"}], "stateMutability": "view", "type": "function"}, {"inputs": [], "name": "MULTIPLIER", "outputs": [{"internalType": "uint256", "name": "", "type": "uint256"}], "stateMutability": "view", "type": "function"}, {"inputs": [], "name": "PERCENTAGE_BASE", "outputs": [{"internalType": "uint256", "name": "", "type": "uint256"}], "stateMutability": "view", "type": "function"}, {"inputs": [], "name": "REWARD_MANAGER", "outputs": [{"internalType": "bytes32", "name": "", "type": "bytes32"}], "stateMutability": "view", "type": "function"}, {"inputs": [], "name": "TOKEN_UNIT", "outputs": [{"internalType": "uint256", "name": "", "type": "uint256"}], "stateMutability": "view", "type": "function"}, {"inputs": [{"internalType": "uint256", "name": "startTime", "type": "uint256"}, {"internalType": "uint256", "name": "duration", "type": "uint256"}, {"internalType": "uint256", "name": "totalReward", "type": "uint256"}, {"internalType": "address", "name": "rewardsToken", "type": "address"}], "name": "addRewardSchedule", "outputs": [], "stateMutability": "nonpayable", "type": "function"}, {"inputs": [{"internalType": "address", "name": "account", "type": "address"}], "name": "balanceOf", "outputs": [{"internalType": "uint256", "name": "", "type": "uint256"}], "stateMutability": "view", "type": "function"}, {"inputs": [], "name": "claimRewards", "outputs": [], "stateMutability": "nonpayable", "type": "function"}, {"inputs": [], "name": "currentScheduleId", "outputs": [{"internalType": "uint256", "name": "", "type": "uint256"}], "stateMutability": "view", "type": "function"}, {"inputs": [{"internalType": "address", "name": "account", "type": "address"}], "name": "earned", "outputs": [{"internalType": "uint256", "name": "total", "type": "uint256"}], "stateMutability": "view", "type": "function"}, {"inputs": [], "name": "getActiveSchedules", "outputs": [{"internalType": "uint256[]", "name": "", "type": "uint256[]"}], "stateMutability": "view", "type": "function"}, {"inputs": [{"internalType": "bytes32", "name": "role", "type": "bytes32"}], "name": "getRoleAdmin", "outputs": [{"internalType": "bytes32", "name": "", "type": "bytes32"}], "stateMutability": "view", "type": "function"}, {"inputs": [{"internalType": "bytes32", "name": "role", "type": "bytes32"}, {"internalType": "address", "name": "account", "type": "address"}], "name": "grantRole", "outputs": [], "stateMutability": "nonpayable", "type": "function"}, {"inputs": [{"internalType": "bytes32", "name": "role", "type": "bytes32"}, {"internalType": "address", "name": "account", "type": "address"}], "name": "hasRole", "outputs": [{"internalType": "bool", "name": "", "type": "bool"}], "stateMutability": "view", "type": "function"}, {"inputs": [], "name": "lastUpdateTime", "outputs": [{"internalType": "uint256", "name": "", "type": "uint256"}], "stateMutability": "view", "type": "function"}, {"inputs": [], "name": "minStakingPeriod", "outputs": [{"internalType": "uint256", "name": "", "type": "uint256"}], "stateMutability": "view", "type": "function"}, {"inputs": [], "name": "pause", "outputs": [], "stateMutability": "nonpayable", "type": "function"}, {"inputs": [{"internalType": "bytes32", "name": "role", "type": "bytes32"}, {"internalType": "address", "name": "account", "type": "address"}], "name": "renounceRole", "outputs": [], "stateMutability": "nonpayable", "type": "function"}, {"inputs": [{"internalType": "bytes32", "name": "role", "type": "bytes32"}, {"internalType": "address", "name": "account", "type": "address"}], "name": "revokeRole", "outputs": [], "stateMutability": "nonpayable", "type": "function"}, {"inputs": [], "name": "rewardPerToken", "outputs": [{"internalType": "uint256", "name": "", "type": "uint256"}], "stateMutability": "view", "type": "function"}, {"inputs": [], "name": "rewardPerTokenStored", "outputs": [{"internalType": "uint256", "name": "", "type": "uint256"}], "stateMutability": "view", "type": "function"}, {"inputs": [{"internalType": "uint256", "name": "", "type": "uint256"}], "name": "rewardSchedules", "outputs": [{"internalType": "uint256", "name": "startTime", "type": "uint256"}, {"internalType": "uint256", "name": "endTime", "type": "uint256"}, {"internalType": "uint256", "name": "totalRewards", "type": "uint256"}, {"internalType": "uint256", "name": "claimedRewards", "type": "uint256"}, {"internalType": "address", "name": "rewardsToken", "type": "address"}], "stateMutability": "view", "type": "function"}, {"inputs": [{"internalType": "address", "name": "", "type": "address"}], "name": "rewards", "outputs": [{"internalType": "uint256", "name": "", "type": "uint256"}], "stateMutability": "view", "type": "function"}, {"inputs": [{"internalType": "uint256", "name": "period", "type": "uint256"}], "name": "setMinStakingPeriod", "outputs": [], "stateMutability": "nonpayable", "type": "function"}, {"inputs": [{"internalType": "uint256", "name": "amount", "type": "uint256"}], "name": "stake", "outputs": [], "stateMutability": "nonpayable", "type": "function"}, {"inputs": [], "name": "stakingToken", "outputs": [{"internalType": "contract IERC20", "name": "", "type": "address"}], "stateMutability": "view", "type": "function"}, {"inputs": [{"internalType": "bytes4", "name": "interfaceId", "type": "bytes4"}], "name": "supportsInterface", "outputs": [{"internalType": "bool", "name": "", "type": "bool"}], "stateMutability": "view", "type": "function"}, {"inputs": [], "name": "totalStaked", "outputs": [{"internalType": "uint256", "name": "", "type": "uint256"}], "stateMutability": "view", "type": "function"}, {"inputs": [], "name": "unpause", "outputs": [], "stateMutability": "nonpayable", "type": "function"}, {"inputs": [{"internalType": "address", "name": "", "type": "address"}], "name": "userRewardPerTokenPaid", "outputs": [{"internalType": "uint256", "name": "", "type": "uint256"}], "stateMutability": "view", "type": "function"}, {"inputs": [{"internalType": "uint256", "name": "amount", "type": "uint256"}], "name": "withdraw", "outputs": [], "stateMutability": "nonpayable", "type": "function"}], "bytecode": "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", "deployedBytecode": "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", "linkReferences": {}, "deployedLinkReferences": {}}