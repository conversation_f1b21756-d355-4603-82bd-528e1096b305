// SPDX-License-Identifier: MIT
pragma solidity 0.8.20;

import "@openzeppelin/contracts/token/ERC721/IERC721.sol";
import "@openzeppelin/contracts/token/ERC20/IERC20.sol";
import "@openzeppelin/contracts/security/ReentrancyGuard.sol";
import "@openzeppelin/contracts/access/AccessControl.sol";

/// @title PropertyMarketOptimized - Optimized marketplace for real estate NFTs
contract PropertyMarketOptimized is ReentrancyGuard, AccessControl {
    
    // ========== Constants ==========
    uint256 public constant PERCENTAGE_BASE = 10000;
    uint256 public constant MIN_BID_INCREMENT = 5; // 5%
    uint256 public constant MAX_BIDS = 100;
    
    // ========== Data Structures ==========
    enum Status { LISTED, SOLD, PENDING_CONFIRMATION }
    
    struct Listing {
        address seller;
        uint96 price; // Packed to save storage
        address paymentToken;
        Status status;
        uint32 confirmationPeriod;
        uint32 listTime;
    }
    
    struct Bid {
        address bidder;
        uint96 amount;
        address paymentToken;
        uint32 timestamp;
        bool active;
    }
    
    struct PendingPurchase {
        address buyer;
        uint96 price;
        address paymentToken;
        uint32 deadline;
        bool active;
    }
    
    // ========== State Variables ==========
    IERC721 public immutable nftiContract;
    
    mapping(uint256 => Listing) public listings;
    mapping(uint256 => PendingPurchase) public pendingPurchases;
    mapping(uint256 => Bid[]) public bidsForToken;
    mapping(address => mapping(uint256 => uint256)) public bidIndexByBidder;
    mapping(address => bool) public allowedTokens;
    
    // Fee configuration
    uint256 public baseFee = 200; // 2%
    address public feeCollector;
    
    // ========== Events ==========
    event Listed(uint256 indexed tokenId, address indexed seller, uint256 price, address token);
    event Sold(uint256 indexed tokenId, address indexed buyer, uint256 price, address token);
    event BidPlaced(uint256 indexed tokenId, address indexed bidder, uint256 amount, address token);
    event BidAccepted(uint256 indexed tokenId, address indexed seller, address indexed bidder, uint256 amount, address token);
    event PurchaseRequested(uint256 indexed tokenId, address indexed buyer, uint256 price, address token, uint256 deadline);
    event PurchaseConfirmed(uint256 indexed tokenId, address indexed seller, address indexed buyer, uint256 price, address token);
    event PurchaseRejected(uint256 indexed tokenId, address indexed seller, address indexed buyer, uint256 price, address token);
    event PurchaseExpired(uint256 indexed tokenId, address indexed buyer, uint256 price, address token);
    
    // ========== Errors ==========
    error InvalidAddress();
    error InvalidAmount();
    error NotOwner();
    error NotListed();
    error AlreadyListed();
    error TokenNotAllowed();
    error InsufficientPayment();
    error NoPendingPurchase();
    error NotActive();
    error NotSeller();
    error ConfirmationExpired();
    error ConfirmationNotExpired();
    error TransferFailed();
    error InvalidBid();
    error TooManyBids();
    
    // ========== Constructor ==========
    constructor(address _nftiAddress, address _feeCollector) {
        if (_nftiAddress == address(0) || _feeCollector == address(0)) revert InvalidAddress();
        
        nftiContract = IERC721(_nftiAddress);
        feeCollector = _feeCollector;
        allowedTokens[address(0)] = true; // Allow ETH
        
        _grantRole(DEFAULT_ADMIN_ROLE, msg.sender);
    }
    
    // ========== Modifiers ==========
    modifier validAmount(uint256 amount) {
        if (amount == 0) revert InvalidAmount();
        _;
    }
    
    modifier onlyTokenOwner(uint256 tokenId) {
        if (nftiContract.ownerOf(tokenId) != msg.sender) revert NotOwner();
        _;
    }
    
    modifier allowedToken(address token) {
        if (!allowedTokens[token]) revert TokenNotAllowed();
        _;
    }
    
    // ========== Core Functions ==========
    
    /// @notice List property for sale
    function listProperty(
        uint256 tokenId,
        uint256 price,
        address paymentToken
    ) external nonReentrant onlyTokenOwner(tokenId) validAmount(price) allowedToken(paymentToken) {
        _listProperty(tokenId, price, paymentToken, 0);
    }
    
    /// @notice List property with confirmation period
    function listPropertyWithConfirmation(
        uint256 tokenId,
        uint256 price,
        address paymentToken,
        uint256 confirmationPeriod
    ) external nonReentrant onlyTokenOwner(tokenId) validAmount(price) allowedToken(paymentToken) {
        if (confirmationPeriod > 7 days) revert InvalidAmount();
        _listProperty(tokenId, price, paymentToken, confirmationPeriod);
    }
    
    function _listProperty(
        uint256 tokenId,
        uint256 price,
        address paymentToken,
        uint256 confirmationPeriod
    ) internal {
        Listing storage listing = listings[tokenId];
        if (listing.seller != address(0) && listing.status == Status.LISTED) revert AlreadyListed();
        
        listings[tokenId] = Listing({
            seller: msg.sender,
            price: uint96(price),
            paymentToken: paymentToken,
            status: Status.LISTED,
            confirmationPeriod: uint32(confirmationPeriod),
            listTime: uint32(block.timestamp)
        });
        
        emit Listed(tokenId, msg.sender, price, paymentToken);
    }
    
    /// @notice Purchase property
    function purchaseProperty(uint256 tokenId, uint256 offerPrice) 
        external payable nonReentrant validAmount(offerPrice) 
    {
        Listing storage listing = listings[tokenId];
        if (listing.status != Status.LISTED) revert NotListed();
        if (offerPrice < listing.price) revert InsufficientPayment();
        
        uint256 highestBid = _getHighestBid(tokenId);
        uint256 actualPrice = highestBid > 0 ? offerPrice : listing.price;
        
        if (listing.confirmationPeriod > 0) {
            _createPendingPurchase(tokenId, actualPrice, listing.paymentToken);
        } else {
            _completePurchase(tokenId, actualPrice, listing.paymentToken);
        }
    }
    
    function _createPendingPurchase(uint256 tokenId, uint256 price, address paymentToken) internal {
        // Lock funds
        if (paymentToken == address(0)) {
            if (msg.value < price) revert InsufficientPayment();
        } else {
            if (!IERC20(paymentToken).transferFrom(msg.sender, address(this), price)) revert TransferFailed();
        }
        
        Listing storage listing = listings[tokenId];
        listing.status = Status.PENDING_CONFIRMATION;
        
        uint256 deadline = block.timestamp + listing.confirmationPeriod;
        pendingPurchases[tokenId] = PendingPurchase({
            buyer: msg.sender,
            price: uint96(price),
            paymentToken: paymentToken,
            deadline: uint32(deadline),
            active: true
        });
        
        emit PurchaseRequested(tokenId, msg.sender, price, paymentToken, deadline);
    }
    
    function _completePurchase(uint256 tokenId, uint256 price, address paymentToken) internal {
        Listing storage listing = listings[tokenId];
        listing.status = Status.SOLD;
        
        _cancelAllBids(tokenId);
        _processPayment(listing.seller, price, paymentToken);
        nftiContract.safeTransferFrom(listing.seller, msg.sender, tokenId, "");
        
        emit Sold(tokenId, msg.sender, price, paymentToken);
    }
    
    /// @notice Seller confirms purchase
    function confirmPurchase(uint256 tokenId) external nonReentrant {
        Listing storage listing = listings[tokenId];
        PendingPurchase storage purchase = pendingPurchases[tokenId];
        
        if (listing.status != Status.PENDING_CONFIRMATION) revert NoPendingPurchase();
        if (!purchase.active) revert NotActive();
        if (nftiContract.ownerOf(tokenId) != msg.sender) revert NotSeller();
        if (block.timestamp > purchase.deadline) revert ConfirmationExpired();
        
        purchase.active = false;
        listing.status = Status.SOLD;
        
        _cancelAllBids(tokenId);
        _processPayment(listing.seller, purchase.price, purchase.paymentToken);
        nftiContract.safeTransferFrom(listing.seller, purchase.buyer, tokenId, "");
        
        emit PurchaseConfirmed(tokenId, msg.sender, purchase.buyer, purchase.price, purchase.paymentToken);
        emit Sold(tokenId, purchase.buyer, purchase.price, purchase.paymentToken);
    }
    
    /// @notice Seller rejects purchase
    function rejectPurchase(uint256 tokenId) external nonReentrant {
        Listing storage listing = listings[tokenId];
        PendingPurchase storage purchase = pendingPurchases[tokenId];
        
        if (listing.status != Status.PENDING_CONFIRMATION) revert NoPendingPurchase();
        if (!purchase.active) revert NotActive();
        if (nftiContract.ownerOf(tokenId) != msg.sender) revert NotSeller();
        if (block.timestamp > purchase.deadline) revert ConfirmationExpired();
        
        _refundPurchase(tokenId);
        purchase.active = false;
        listing.status = Status.LISTED;
        
        emit PurchaseRejected(tokenId, msg.sender, purchase.buyer, purchase.price, purchase.paymentToken);
    }
    
    /// @notice Cancel expired purchase
    function cancelExpiredPurchase(uint256 tokenId) external nonReentrant {
        Listing storage listing = listings[tokenId];
        PendingPurchase storage purchase = pendingPurchases[tokenId];
        
        if (listing.status != Status.PENDING_CONFIRMATION) revert NoPendingPurchase();
        if (!purchase.active) revert NotActive();
        if (block.timestamp <= purchase.deadline) revert ConfirmationNotExpired();
        
        _refundPurchase(tokenId);
        purchase.active = false;
        listing.status = Status.LISTED;
        
        emit PurchaseExpired(tokenId, purchase.buyer, purchase.price, purchase.paymentToken);
    }
    
    function _refundPurchase(uint256 tokenId) internal {
        PendingPurchase storage purchase = pendingPurchases[tokenId];
        
        if (purchase.paymentToken == address(0)) {
            (bool success, ) = payable(purchase.buyer).call{value: purchase.price}("");
            if (!success) revert TransferFailed();
        } else {
            if (!IERC20(purchase.paymentToken).transfer(purchase.buyer, purchase.price)) revert TransferFailed();
        }
    }
    
    function _processPayment(address seller, uint256 amount, address paymentToken) internal {
        uint256 fee = (amount * baseFee) / PERCENTAGE_BASE;
        uint256 netAmount = amount - fee;
        
        if (paymentToken == address(0)) {
            if (msg.value < amount) revert InsufficientPayment();
            
            (bool success1, ) = payable(seller).call{value: netAmount}("");
            (bool success2, ) = payable(feeCollector).call{value: fee}("");
            if (!success1 || !success2) revert TransferFailed();
            
            // Refund excess
            if (msg.value > amount) {
                (bool success3, ) = payable(msg.sender).call{value: msg.value - amount}("");
                if (!success3) revert TransferFailed();
            }
        } else {
            IERC20 token = IERC20(paymentToken);
            if (!token.transferFrom(msg.sender, seller, netAmount)) revert TransferFailed();
            if (!token.transferFrom(msg.sender, feeCollector, fee)) revert TransferFailed();
        }
    }

    // ========== Bidding Functions ==========

    /// @notice Place bid on property
    function placeBid(uint256 tokenId, uint256 bidAmount, address paymentToken)
        external payable nonReentrant validAmount(bidAmount) allowedToken(paymentToken)
    {
        Listing storage listing = listings[tokenId];
        if (listing.status != Status.LISTED) revert NotListed();
        if (nftiContract.ownerOf(tokenId) == msg.sender) revert InvalidBid();
        if (bidAmount < listing.price) revert InvalidBid();

        Bid[] storage bids = bidsForToken[tokenId];
        if (bids.length >= MAX_BIDS) revert TooManyBids();

        uint256 highestBid = _getHighestBid(tokenId);
        if (highestBid > 0) {
            uint256 minBid = highestBid * (100 + MIN_BID_INCREMENT) / 100;
            if (bidAmount < minBid) revert InvalidBid();
        }

        // Handle existing bid update
        uint256 existingIndex = bidIndexByBidder[msg.sender][tokenId];
        if (existingIndex > 0) {
            Bid storage existingBid = bids[existingIndex - 1];
            if (!existingBid.active) revert NotActive();

            // Refund previous bid
            _refundBid(existingBid.bidder, existingBid.amount, existingBid.paymentToken);

            // Update existing bid
            existingBid.amount = uint96(bidAmount);
            existingBid.timestamp = uint32(block.timestamp);
        } else {
            // Create new bid
            bids.push(Bid({
                bidder: msg.sender,
                amount: uint96(bidAmount),
                paymentToken: paymentToken,
                timestamp: uint32(block.timestamp),
                active: true
            }));
            bidIndexByBidder[msg.sender][tokenId] = bids.length;
        }

        // Lock bid funds
        if (paymentToken == address(0)) {
            if (msg.value != bidAmount) revert InsufficientPayment();
        } else {
            if (!IERC20(paymentToken).transferFrom(msg.sender, address(this), bidAmount)) revert TransferFailed();
        }

        emit BidPlaced(tokenId, msg.sender, bidAmount, paymentToken);
    }

    /// @notice Accept bid
    function acceptBid(uint256 tokenId, uint256 bidIndex) external nonReentrant onlyTokenOwner(tokenId) {
        Listing storage listing = listings[tokenId];
        if (listing.status != Status.LISTED) revert NotListed();

        Bid[] storage bids = bidsForToken[tokenId];
        if (bidIndex >= bids.length) revert InvalidBid();

        Bid storage bid = bids[bidIndex];
        if (!bid.active) revert NotActive();

        listing.status = Status.SOLD;
        bid.active = false;
        bidIndexByBidder[bid.bidder][tokenId] = 0;

        _cancelAllBids(tokenId);
        _processPaymentFromBid(listing.seller, bid.amount, bid.paymentToken);
        nftiContract.safeTransferFrom(listing.seller, bid.bidder, tokenId, "");

        emit BidAccepted(tokenId, listing.seller, bid.bidder, bid.amount, bid.paymentToken);
        emit Sold(tokenId, bid.bidder, bid.amount, bid.paymentToken);
    }

    /// @notice Cancel bid
    function cancelBid(uint256 tokenId) external nonReentrant {
        uint256 bidIndex = bidIndexByBidder[msg.sender][tokenId];
        if (bidIndex == 0) revert InvalidBid();

        Bid[] storage bids = bidsForToken[tokenId];
        Bid storage bid = bids[bidIndex - 1];
        if (!bid.active) revert NotActive();

        bid.active = false;
        bidIndexByBidder[msg.sender][tokenId] = 0;

        _refundBid(bid.bidder, bid.amount, bid.paymentToken);
    }

    function _getHighestBid(uint256 tokenId) internal view returns (uint256 highest) {
        Bid[] storage bids = bidsForToken[tokenId];
        for (uint256 i = 0; i < bids.length; i++) {
            if (bids[i].active && bids[i].amount > highest) {
                highest = bids[i].amount;
            }
        }
    }

    function _cancelAllBids(uint256 tokenId) internal {
        Bid[] storage bids = bidsForToken[tokenId];
        for (uint256 i = 0; i < bids.length; i++) {
            if (bids[i].active) {
                bids[i].active = false;
                bidIndexByBidder[bids[i].bidder][tokenId] = 0;
                _refundBid(bids[i].bidder, bids[i].amount, bids[i].paymentToken);
            }
        }
    }

    function _refundBid(address bidder, uint256 amount, address paymentToken) internal {
        if (paymentToken == address(0)) {
            (bool success, ) = payable(bidder).call{value: amount}("");
            if (!success) revert TransferFailed();
        } else {
            if (!IERC20(paymentToken).transfer(bidder, amount)) revert TransferFailed();
        }
    }

    function _processPaymentFromBid(address seller, uint256 amount, address paymentToken) internal {
        uint256 fee = (amount * baseFee) / PERCENTAGE_BASE;
        uint256 netAmount = amount - fee;

        if (paymentToken == address(0)) {
            (bool success1, ) = payable(seller).call{value: netAmount}("");
            (bool success2, ) = payable(feeCollector).call{value: fee}("");
            if (!success1 || !success2) revert TransferFailed();
        } else {
            IERC20 token = IERC20(paymentToken);
            if (!token.transfer(seller, netAmount)) revert TransferFailed();
            if (!token.transfer(feeCollector, fee)) revert TransferFailed();
        }
    }

    // ========== View Functions ==========

    function getListingDetails(uint256 tokenId) external view returns (
        address seller,
        uint256 price,
        address paymentToken,
        Status status,
        uint256 confirmationPeriod,
        uint256 listTime
    ) {
        Listing storage listing = listings[tokenId];
        return (
            listing.seller,
            listing.price,
            listing.paymentToken,
            listing.status,
            listing.confirmationPeriod,
            listing.listTime
        );
    }

    function getPendingPurchaseDetails(uint256 tokenId) external view returns (
        address buyer,
        uint256 price,
        address paymentToken,
        uint256 deadline,
        bool active,
        bool expired
    ) {
        PendingPurchase storage purchase = pendingPurchases[tokenId];
        return (
            purchase.buyer,
            purchase.price,
            purchase.paymentToken,
            purchase.deadline,
            purchase.active,
            block.timestamp > purchase.deadline
        );
    }

    function getActiveBids(uint256 tokenId) external view returns (Bid[] memory activeBids) {
        Bid[] storage allBids = bidsForToken[tokenId];
        uint256 activeCount = 0;

        // Count active bids
        for (uint256 i = 0; i < allBids.length; i++) {
            if (allBids[i].active) activeCount++;
        }

        // Create array of active bids
        activeBids = new Bid[](activeCount);
        uint256 index = 0;
        for (uint256 i = 0; i < allBids.length; i++) {
            if (allBids[i].active) {
                activeBids[index] = allBids[i];
                index++;
            }
        }
    }

    function requiresSellerConfirmation(uint256 tokenId) external view returns (bool) {
        return listings[tokenId].confirmationPeriod > 0;
    }

    // ========== Admin Functions ==========

    function setPaymentTokenAllowed(address token, bool allowed) external onlyRole(DEFAULT_ADMIN_ROLE) {
        allowedTokens[token] = allowed;
    }

    function setBaseFee(uint256 newFee) external onlyRole(DEFAULT_ADMIN_ROLE) {
        if (newFee > 1000) revert InvalidAmount(); // Max 10%
        baseFee = newFee;
    }

    function setFeeCollector(address newCollector) external onlyRole(DEFAULT_ADMIN_ROLE) {
        if (newCollector == address(0)) revert InvalidAddress();
        feeCollector = newCollector;
    }

    // ========== Emergency Functions ==========

    function emergencyWithdraw(address token, uint256 amount, address recipient) external onlyRole(DEFAULT_ADMIN_ROLE) {
        if (recipient == address(0)) revert InvalidAddress();

        if (token == address(0)) {
            (bool success, ) = payable(recipient).call{value: amount}("");
            if (!success) revert TransferFailed();
        } else {
            if (!IERC20(token).transfer(recipient, amount)) revert TransferFailed();
        }
    }
}
