{"_format": "hh-sol-artifact-1", "contractName": "StakingConstants", "sourceName": "contracts/libraries/StakingConstants.sol", "abi": [{"inputs": [], "name": "MAX_STAKING_PERIOD", "outputs": [{"internalType": "uint256", "name": "", "type": "uint256"}], "stateMutability": "view", "type": "function"}, {"inputs": [], "name": "MIN_STAKING_PERIOD", "outputs": [{"internalType": "uint256", "name": "", "type": "uint256"}], "stateMutability": "view", "type": "function"}], "bytecode": "0x609d610038600b82828239805160001a607314602b57634e487b7160e01b600052600060045260246000fd5b30600052607381538281f3fe7300000000000000000000000000000000000000003014608060405260043610603d5760003560e01c8063c743dabb146042578063cac33fdc14605d575b600080fd5b604b62093a8081565b60405190815260200160405180910390f35b604b6301e133808156fea2646970667358221220ccde0ac395de464d9ebb73040c678793580027ec19e2f5675548d6bc39e0c3a964736f6c63430008140033", "deployedBytecode": "0x7300000000000000000000000000000000000000003014608060405260043610603d5760003560e01c8063c743dabb146042578063cac33fdc14605d575b600080fd5b604b62093a8081565b60405190815260200160405180910390f35b604b6301e133808156fea2646970667358221220ccde0ac395de464d9ebb73040c678793580027ec19e2f5675548d6bc39e0c3a964736f6c63430008140033", "linkReferences": {}, "deployedLinkReferences": {}}