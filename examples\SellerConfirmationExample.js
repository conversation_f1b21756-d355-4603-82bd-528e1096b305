// 卖家反悔时间功能使用示例

const { ethers } = require("ethers");

class SellerConfirmationExample {
    constructor(propertyMarketContract, provider) {
        this.propertyMarket = propertyMarketContract;
        this.provider = provider;
    }

    // 1. 卖家挂牌房产时设置确认时间
    async listPropertyWithConfirmation(tokenId, price, paymentToken, confirmationHours = 24) {
        const confirmationPeriod = confirmationHours * 3600; // 转换为秒
        
        try {
            const tx = await this.propertyMarket.listPropertyWithConfirmation(
                tokenId,
                ethers.utils.parseEther(price.toString()),
                paymentToken,
                confirmationPeriod
            );
            
            console.log(`房产 ${tokenId} 已挂牌，价格: ${price} ETH`);
            console.log(`卖家确认时间: ${confirmationHours} 小时`);
            console.log(`交易哈希: ${tx.hash}`);
            
            return tx;
        } catch (error) {
            console.error("挂牌失败:", error.message);
            throw error;
        }
    }

    // 2. 买家发起购买请求
    async requestPurchase(tokenId, offerPrice) {
        try {
            const tx = await this.propertyMarket.purchaseProperty(
                tokenId,
                ethers.utils.parseEther(offerPrice.toString()),
                { value: ethers.utils.parseEther(offerPrice.toString()) }
            );
            
            console.log(`购买请求已发送，房产 ${tokenId}，出价: ${offerPrice} ETH`);
            console.log(`等待卖家确认...`);
            console.log(`交易哈希: ${tx.hash}`);
            
            return tx;
        } catch (error) {
            console.error("购买请求失败:", error.message);
            throw error;
        }
    }

    // 3. 卖家确认购买
    async confirmPurchase(tokenId) {
        try {
            const tx = await this.propertyMarket.confirmPurchase(tokenId);
            
            console.log(`卖家已确认购买，房产 ${tokenId} 交易完成`);
            console.log(`交易哈希: ${tx.hash}`);
            
            return tx;
        } catch (error) {
            console.error("确认购买失败:", error.message);
            throw error;
        }
    }

    // 4. 卖家拒绝购买
    async rejectPurchase(tokenId) {
        try {
            const tx = await this.propertyMarket.rejectPurchase(tokenId);
            
            console.log(`卖家已拒绝购买，房产 ${tokenId} 恢复挂牌状态`);
            console.log(`买家资金已退还`);
            console.log(`交易哈希: ${tx.hash}`);
            
            return tx;
        } catch (error) {
            console.error("拒绝购买失败:", error.message);
            throw error;
        }
    }

    // 5. 取消过期的购买订单
    async cancelExpiredPurchase(tokenId) {
        try {
            const tx = await this.propertyMarket.cancelExpiredPurchase(tokenId);
            
            console.log(`过期购买订单已取消，房产 ${tokenId}`);
            console.log(`买家资金已退还`);
            console.log(`交易哈希: ${tx.hash}`);
            
            return tx;
        } catch (error) {
            console.error("取消过期订单失败:", error.message);
            throw error;
        }
    }

    // 6. 查询待确认的购买订单
    async getPendingPurchaseInfo(tokenId) {
        try {
            const details = await this.propertyMarket.getPendingPurchaseDetails(tokenId);
            
            const info = {
                buyer: details.buyer,
                offerPrice: ethers.utils.formatEther(details.offerPrice),
                paymentToken: details.paymentToken,
                purchaseTime: new Date(details.purchaseTimestamp * 1000),
                deadline: new Date(details.confirmationDeadline * 1000),
                isActive: details.isActive,
                isExpired: details.isExpired
            };
            
            console.log(`待确认购买订单信息:`, info);
            return info;
        } catch (error) {
            console.error("查询失败:", error.message);
            throw error;
        }
    }

    // 7. 监听相关事件
    setupEventListeners() {
        // 监听购买请求事件
        this.propertyMarket.on("PurchaseRequested", (tokenId, buyer, offerPrice, paymentToken, deadline) => {
            console.log(`🔔 新的购买请求:`);
            console.log(`  房产ID: ${tokenId}`);
            console.log(`  买家: ${buyer}`);
            console.log(`  出价: ${ethers.utils.formatEther(offerPrice)} ETH`);
            console.log(`  确认截止时间: ${new Date(deadline * 1000)}`);
        });

        // 监听购买确认事件
        this.propertyMarket.on("PurchaseConfirmed", (tokenId, seller, buyer, finalPrice, paymentToken) => {
            console.log(`✅ 购买已确认:`);
            console.log(`  房产ID: ${tokenId}`);
            console.log(`  卖家: ${seller}`);
            console.log(`  买家: ${buyer}`);
            console.log(`  成交价: ${ethers.utils.formatEther(finalPrice)} ETH`);
        });

        // 监听购买拒绝事件
        this.propertyMarket.on("PurchaseRejected", (tokenId, seller, buyer, offerPrice, paymentToken) => {
            console.log(`❌ 购买已拒绝:`);
            console.log(`  房产ID: ${tokenId}`);
            console.log(`  卖家: ${seller}`);
            console.log(`  买家: ${buyer}`);
            console.log(`  出价: ${ethers.utils.formatEther(offerPrice)} ETH`);
        });

        // 监听购买过期事件
        this.propertyMarket.on("PurchaseExpired", (tokenId, buyer, offerPrice, paymentToken) => {
            console.log(`⏰ 购买订单已过期:`);
            console.log(`  房产ID: ${tokenId}`);
            console.log(`  买家: ${buyer}`);
            console.log(`  出价: ${ethers.utils.formatEther(offerPrice)} ETH`);
        });
    }
}

// 使用示例
async function example() {
    // 假设已经连接到合约
    const propertyMarket = new ethers.Contract(contractAddress, abi, signer);
    const example = new SellerConfirmationExample(propertyMarket, provider);
    
    // 设置事件监听
    example.setupEventListeners();
    
    // 1. 卖家挂牌房产，设置24小时确认时间
    await example.listPropertyWithConfirmation(1, 100, ethers.constants.AddressZero, 24);
    
    // 2. 买家发起购买请求
    await example.requestPurchase(1, 100);
    
    // 3. 查询待确认订单
    await example.getPendingPurchaseInfo(1);
    
    // 4. 卖家可以选择确认或拒绝
    // await example.confirmPurchase(1);  // 确认
    // await example.rejectPurchase(1);   // 拒绝
}

module.exports = SellerConfirmationExample;
