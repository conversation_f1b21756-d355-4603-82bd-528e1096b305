{"_format": "hh-sol-artifact-1", "contractName": "GnosisStyleMultiSig", "sourceName": "contracts/governance/GnosisStyleMultiSig.sol", "abi": [{"inputs": [{"internalType": "address[]", "name": "_owners", "type": "address[]"}, {"internalType": "uint256", "name": "_threshold", "type": "uint256"}], "stateMutability": "nonpayable", "type": "constructor"}, {"inputs": [], "name": "Duplicate<PERSON><PERSON>er", "type": "error"}, {"inputs": [], "name": "InvalidOwner", "type": "error"}, {"inputs": [], "name": "InvalidSignature", "type": "error"}, {"inputs": [], "name": "InvalidThreshold", "type": "error"}, {"inputs": [], "name": "NotEnoughOwners", "type": "error"}, {"inputs": [], "name": "TransactionFailed", "type": "error"}, {"inputs": [], "name": "Unauthorized", "type": "error"}, {"anonymous": false, "inputs": [{"indexed": true, "internalType": "bytes32", "name": "txHash", "type": "bytes32"}, {"indexed": false, "internalType": "uint256", "name": "payment", "type": "uint256"}], "name": "ExecutionFailure", "type": "event"}, {"anonymous": false, "inputs": [{"indexed": true, "internalType": "bytes32", "name": "txHash", "type": "bytes32"}, {"indexed": false, "internalType": "uint256", "name": "payment", "type": "uint256"}], "name": "ExecutionSuccess", "type": "event"}, {"anonymous": false, "inputs": [{"indexed": true, "internalType": "address", "name": "owner", "type": "address"}], "name": "OwnerAdded", "type": "event"}, {"anonymous": false, "inputs": [{"indexed": true, "internalType": "address", "name": "owner", "type": "address"}], "name": "OwnerRemoved", "type": "event"}, {"anonymous": false, "inputs": [{"indexed": false, "internalType": "uint256", "name": "threshold", "type": "uint256"}], "name": "Thr<PERSON>oldChanged", "type": "event"}, {"stateMutability": "payable", "type": "fallback"}, {"inputs": [], "name": "DOMAIN_SEPARATOR", "outputs": [{"internalType": "bytes32", "name": "", "type": "bytes32"}], "stateMutability": "view", "type": "function"}, {"inputs": [], "name": "TRANSACTION_TYPEHASH", "outputs": [{"internalType": "bytes32", "name": "", "type": "bytes32"}], "stateMutability": "view", "type": "function"}, {"inputs": [{"internalType": "address", "name": "owner", "type": "address"}, {"internalType": "uint256", "name": "_threshold", "type": "uint256"}], "name": "addOwnerWithThreshold", "outputs": [], "stateMutability": "nonpayable", "type": "function"}, {"inputs": [{"internalType": "uint256", "name": "_threshold", "type": "uint256"}], "name": "changeThreshold", "outputs": [], "stateMutability": "nonpayable", "type": "function"}, {"inputs": [{"internalType": "bytes32", "name": "txHash", "type": "bytes32"}, {"internalType": "bytes", "name": "signatures", "type": "bytes"}], "name": "checkSignatures", "outputs": [{"internalType": "bool", "name": "", "type": "bool"}], "stateMutability": "view", "type": "function"}, {"inputs": [{"internalType": "address", "name": "to", "type": "address"}, {"internalType": "uint256", "name": "value", "type": "uint256"}, {"internalType": "bytes", "name": "data", "type": "bytes"}, {"internalType": "bytes", "name": "signatures", "type": "bytes"}], "name": "execTransaction", "outputs": [{"internalType": "bool", "name": "success", "type": "bool"}], "stateMutability": "payable", "type": "function"}, {"inputs": [], "name": "getOwnerCount", "outputs": [{"internalType": "uint256", "name": "", "type": "uint256"}], "stateMutability": "view", "type": "function"}, {"inputs": [], "name": "getOwners", "outputs": [{"internalType": "address[]", "name": "", "type": "address[]"}], "stateMutability": "view", "type": "function"}, {"inputs": [{"internalType": "address", "name": "to", "type": "address"}, {"internalType": "uint256", "name": "value", "type": "uint256"}, {"internalType": "bytes", "name": "data", "type": "bytes"}, {"internalType": "uint256", "name": "_nonce", "type": "uint256"}], "name": "getTransactionHash", "outputs": [{"internalType": "bytes32", "name": "", "type": "bytes32"}], "stateMutability": "view", "type": "function"}, {"inputs": [{"internalType": "address", "name": "", "type": "address"}], "name": "isOwner", "outputs": [{"internalType": "bool", "name": "", "type": "bool"}], "stateMutability": "view", "type": "function"}, {"inputs": [], "name": "nonce", "outputs": [{"internalType": "uint256", "name": "", "type": "uint256"}], "stateMutability": "view", "type": "function"}, {"inputs": [{"internalType": "uint256", "name": "", "type": "uint256"}], "name": "owners", "outputs": [{"internalType": "address", "name": "", "type": "address"}], "stateMutability": "view", "type": "function"}, {"inputs": [{"internalType": "address", "name": "prevOwner", "type": "address"}, {"internalType": "address", "name": "owner", "type": "address"}, {"internalType": "uint256", "name": "_threshold", "type": "uint256"}], "name": "removeOwner", "outputs": [], "stateMutability": "nonpayable", "type": "function"}, {"inputs": [], "name": "threshold", "outputs": [{"internalType": "uint256", "name": "", "type": "uint256"}], "stateMutability": "view", "type": "function"}, {"inputs": [{"internalType": "bytes32", "name": "txHash", "type": "bytes32"}, {"internalType": "bytes", "name": "signatures", "type": "bytes"}], "name": "validateSignatures", "outputs": [], "stateMutability": "view", "type": "function"}, {"stateMutability": "payable", "type": "receive"}], "bytecode": "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", "deployedBytecode": "0x6080604052600436106100e05760003560e01c8063a0e67e2b11610084578063d78575c511610056578063d78575c51461029d578063ed516d51146102bd578063ef18374a146102dd578063f8dc5dd9146102f257005b8063a0e67e2b14610232578063affed0e014610254578063b98a34de1461026a578063d01aa3181461028a57005b80633644e515116100bd5780633644e5151461018657806342cde4e8146101c8578063694e80c3146101de57806388633b7b146101fe57005b8063025e7c27146100e95780630d582f13146101265780632f54bf6e1461014657005b366100e757005b005b3480156100f557600080fd5b50610109610104366004611082565b610312565b6040516001600160a01b0390911681526020015b60405180910390f35b34801561013257600080fd5b506100e76101413660046110b7565b61033c565b34801561015257600080fd5b506101766101613660046110e1565b60046020526000908152604090205460ff1681565b604051901515815260200161011d565b34801561019257600080fd5b506101ba7f000000000000000000000000000000000000000000000000000000000000000081565b60405190815260200161011d565b3480156101d457600080fd5b506101ba60015481565b3480156101ea57600080fd5b506100e76101f9366004611082565b6104a3565b34801561020a57600080fd5b506101ba7f840997b6f4374e4577fea5fb3db0ff865257d07b88c2c6e15ec156a0be4776cb81565b34801561023e57600080fd5b50610247610529565b60405161011d91906110fc565b34801561026057600080fd5b506101ba60025481565b34801561027657600080fd5b506101ba61028536600461118b565b61058b565b6101766102983660046111ed565b61068f565b3480156102a957600080fd5b506100e76102b8366004611277565b6107db565b3480156102c957600080fd5b506101766102d8366004611277565b610820565b3480156102e957600080fd5b506003546101ba565b3480156102fe57600080fd5b506100e761030d3660046112c3565b61088a565b6003818154811061032257600080fd5b6000918252602090912001546001600160a01b0316905081565b33301461035b576040516282b42960e81b815260040160405180910390fd5b6001600160a01b038216158061038957506001600160a01b03821660009081526004602052604090205460ff165b156103a7576040516349e27cff60e01b815260040160405180910390fd5b8015806103c057506003546103bd906001611315565b81115b156103de5760405163aabd5a0960e01b815260040160405180910390fd5b6003805460018082019092557fc2575a0e9e593c00f959f8c92f12db2869c3395a3b0502d05e2516446f71f85b0180546001600160a01b0319166001600160a01b038516908117909155600081815260046020526040808220805460ff19168517905592849055915190917f994a936646fe87ffe4f1e469d3d6aa417d6b855598397f323de5b449f765f0c391a26040518181527f6c4ce60fd690e1216286a10b875c5662555f10774484e58142cedd7a90781baa9060200160405180910390a15050565b3330146104c2576040516282b42960e81b815260040160405180910390fd5b8015806104d0575060035481115b156104ee5760405163aabd5a0960e01b815260040160405180910390fd5b60018190556040518181527f6c4ce60fd690e1216286a10b875c5662555f10774484e58142cedd7a90781baa9060200160405180910390a150565b6060600380548060200260200160405190810160405280929190818152602001828054801561058157602002820191906000526020600020905b81546001600160a01b03168152600190910190602001808311610563575b5050505050905090565b6000601960f81b600160f81b7f00000000000000000000000000000000000000000000000000000000000000007f840997b6f4374e4577fea5fb3db0ff865257d07b88c2c6e15ec156a0be4776cb898989896040516105eb929190611328565b60405190819003812061062c949392918a906020019485526001600160a01b0393909316602085015260408401919091526060830152608082015260a00190565b60408051808303601f190181529082905280516020918201206001600160f81b0319958616918301919091529290931660218401526022830152604282015260620160405160208183030381529060405280519060200120905095945050505050565b6000610699610a9e565b60006106aa8888888860025461058b565b6002805491925060006106bc83611338565b91905055506107018185858080601f016020809104026020016040519081016040528093929190818152602001838380828437600092019190915250610afc92505050565b610742888888888080601f016020809104026020016040519081016040528093929190818152602001838380828437600092019190915250610d2992505050565b9150811561078a57807f442e715f626346e8c54381002da614f62bee8d27386535b2521ec8540898556e600060405161077d91815260200190565b60405180910390a26107c6565b807f23428b18acfb3ea64b08dc0c1d296ea9c09702c09083ca5272e64d115b687d2360006040516107bd91815260200190565b60405180910390a25b506107d16001600055565b9695505050505050565b61081b8383838080601f016020809104026020016040519081016040528093929190818152602001838380828437600092019190915250610afc92505050565b505050565b60405163d78575c560e01b8152600090309063d78575c59061084a90879087908790600401611351565b60006040518083038186803b15801561086257600080fd5b505afa925050508015610873575060015b61087f57506000610883565b5060015b9392505050565b3330146108a9576040516282b42960e81b815260040160405180910390fd5b6001600160a01b03821660009081526004602052604090205460ff166108e2576040516349e27cff60e01b815260040160405180910390fd5b60035481906108f390600190611387565b10156109125760405163aabd5a0960e01b815260040160405180910390fd5b60005b600354811015610a1a57826001600160a01b03166003828154811061093c5761093c61139a565b6000918252602090912001546001600160a01b031603610a08576003805461096690600190611387565b815481106109765761097661139a565b600091825260209091200154600380546001600160a01b0390921691839081106109a2576109a261139a565b9060005260206000200160006101000a8154816001600160a01b0302191690836001600160a01b0316021790555060038054806109e1576109e16113b0565b600082815260209020810160001990810180546001600160a01b0319169055019055610a1a565b80610a1281611338565b915050610915565b506001600160a01b038216600081815260046020526040808220805460ff191690556001849055517f58619076adf5bb0943d100ef88d52d7c3fd691b19d3a9071b555b651fbf418da9190a26040518181527f6c4ce60fd690e1216286a10b875c5662555f10774484e58142cedd7a90781baa9060200160405180910390a1505050565b600260005403610af55760405162461bcd60e51b815260206004820152601f60248201527f5265656e7472616e637947756172643a207265656e7472616e742063616c6c0060448201526064015b60405180910390fd5b6002600055565b600060418251610b0c91906113c6565b9050600154811015610b585760405162461bcd60e51b81526020600482015260156024820152744e6f7420656e6f756768207369676e61747572657360581b6044820152606401610aec565b60008167ffffffffffffffff811115610b7357610b736113e8565b604051908082528060200260200182016040528015610b9c578160200160208202803683370190505b5090506000805b83811015610d21576000610bb78683610d40565b9050610bfa81610bf4897f19457468657265756d205369676e6564204d6573736167653a0a3332000000006000908152601c91909152603c902090565b90610e06565b6001600160a01b03811660009081526004602052604090205490935060ff16610c555760405162461bcd60e51b815260206004820152600d60248201526c24b73b30b634b21037bbb732b960991b6044820152606401610aec565b60005b82811015610ce257836001600160a01b0316858281518110610c7c57610c7c61139a565b60200260200101516001600160a01b031603610cd05760405162461bcd60e51b81526020600482015260136024820152724475706c6963617465207369676e617475726560681b6044820152606401610aec565b80610cda81611338565b915050610c58565b5082848381518110610cf657610cf661139a565b6001600160a01b03909216602092830291909101909101525080610d1981611338565b915050610ba3565b505050505050565b600080600083516020850186885af1949350505050565b60606041610d4f836001611315565b610d5991906113fe565b83511015610da95760405162461bcd60e51b815260206004820152601760248201527f496e76616c6964207369676e617475726520696e6465780000000000000000006044820152606401610aec565b60408051604180825260808201909252906020820181803683370190505090506000610dd66041846113fe565b9050806020850101602083018151815260208201516020820152604082015160001a604082015350505092915050565b6000806000610e158585610e2c565b91509150610e2281610e71565b5090505b92915050565b6000808251604103610e625760208301516040840151606085015160001a610e5687828585610fbe565b94509450505050610e6a565b506000905060025b9250929050565b6000816004811115610e8557610e85611415565b03610e8d5750565b6001816004811115610ea157610ea1611415565b03610eee5760405162461bcd60e51b815260206004820152601860248201527f45434453413a20696e76616c6964207369676e617475726500000000000000006044820152606401610aec565b6002816004811115610f0257610f02611415565b03610f4f5760405162461bcd60e51b815260206004820152601f60248201527f45434453413a20696e76616c6964207369676e6174757265206c656e677468006044820152606401610aec565b6003816004811115610f6357610f63611415565b03610fbb5760405162461bcd60e51b815260206004820152602260248201527f45434453413a20696e76616c6964207369676e6174757265202773272076616c604482015261756560f01b6064820152608401610aec565b50565b6000807f7fffffffffffffffffffffffffffffff5d576e7357a4501ddfe92f46681b20a0831115610ff55750600090506003611079565b6040805160008082526020820180845289905260ff881692820192909252606081018690526080810185905260019060a0016020604051602081039080840390855afa158015611049573d6000803e3d6000fd5b5050604051601f1901519150506001600160a01b03811661107257600060019250925050611079565b9150600090505b94509492505050565b60006020828403121561109457600080fd5b5035919050565b80356001600160a01b03811681146110b257600080fd5b919050565b600080604083850312156110ca57600080fd5b6110d38361109b565b946020939093013593505050565b6000602082840312156110f357600080fd5b6108838261109b565b6020808252825182820181905260009190848201906040850190845b8181101561113d5783516001600160a01b031683529284019291840191600101611118565b50909695505050505050565b60008083601f84011261115b57600080fd5b50813567ffffffffffffffff81111561117357600080fd5b602083019150836020828501011115610e6a57600080fd5b6000806000806000608086880312156111a357600080fd5b6111ac8661109b565b945060208601359350604086013567ffffffffffffffff8111156111cf57600080fd5b6111db88828901611149565b96999598509660600135949350505050565b6000806000806000806080878903121561120657600080fd5b61120f8761109b565b955060208701359450604087013567ffffffffffffffff8082111561123357600080fd5b61123f8a838b01611149565b9096509450606089013591508082111561125857600080fd5b5061126589828a01611149565b979a9699509497509295939492505050565b60008060006040848603121561128c57600080fd5b83359250602084013567ffffffffffffffff8111156112aa57600080fd5b6112b686828701611149565b9497909650939450505050565b6000806000606084860312156112d857600080fd5b6112e18461109b565b92506112ef6020850161109b565b9150604084013590509250925092565b634e487b7160e01b600052601160045260246000fd5b80820180821115610e2657610e266112ff565b8183823760009101908152919050565b60006001820161134a5761134a6112ff565b5060010190565b83815260406020820152816040820152818360608301376000818301606090810191909152601f909201601f1916010192915050565b81810381811115610e2657610e266112ff565b634e487b7160e01b600052603260045260246000fd5b634e487b7160e01b600052603160045260246000fd5b6000826113e357634e487b7160e01b600052601260045260246000fd5b500490565b634e487b7160e01b600052604160045260246000fd5b8082028115828204841417610e2657610e266112ff565b634e487b7160e01b600052602160045260246000fdfea2646970667358221220bcd586fadb7d5348b387ef2a24a4116ab6725e6faeac8d6bbd7a58c7a516d36e64736f6c63430008140033", "linkReferences": {}, "deployedLinkReferences": {}}