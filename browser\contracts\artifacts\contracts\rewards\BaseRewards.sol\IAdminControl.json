{"_format": "hh-sol-artifact-1", "contractName": "IAdminControl", "sourceName": "contracts/rewards/BaseRewards.sol", "abi": [{"inputs": [{"internalType": "address", "name": "user", "type": "address"}], "name": "getCommunityScore", "outputs": [{"internalType": "uint256", "name": "", "type": "uint256"}], "stateMutability": "view", "type": "function"}], "bytecode": "0x", "deployedBytecode": "0x", "linkReferences": {}, "deployedLinkReferences": {}}