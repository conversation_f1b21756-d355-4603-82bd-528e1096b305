{"_format": "hh-sol-artifact-1", "contractName": "MockERC20", "sourceName": "contracts/mocks/MockERC20.sol", "abi": [{"inputs": [{"internalType": "string", "name": "name", "type": "string"}, {"internalType": "string", "name": "symbol", "type": "string"}, {"internalType": "uint8", "name": "decimals_", "type": "uint8"}], "stateMutability": "nonpayable", "type": "constructor"}, {"anonymous": false, "inputs": [{"indexed": true, "internalType": "address", "name": "owner", "type": "address"}, {"indexed": true, "internalType": "address", "name": "spender", "type": "address"}, {"indexed": false, "internalType": "uint256", "name": "value", "type": "uint256"}], "name": "Approval", "type": "event"}, {"anonymous": false, "inputs": [{"indexed": true, "internalType": "address", "name": "previousOwner", "type": "address"}, {"indexed": true, "internalType": "address", "name": "new<PERSON>wner", "type": "address"}], "name": "OwnershipTransferred", "type": "event"}, {"anonymous": false, "inputs": [{"indexed": true, "internalType": "address", "name": "from", "type": "address"}, {"indexed": true, "internalType": "address", "name": "to", "type": "address"}, {"indexed": false, "internalType": "uint256", "name": "value", "type": "uint256"}], "name": "Transfer", "type": "event"}, {"inputs": [{"internalType": "address", "name": "owner", "type": "address"}, {"internalType": "address", "name": "spender", "type": "address"}], "name": "allowance", "outputs": [{"internalType": "uint256", "name": "", "type": "uint256"}], "stateMutability": "view", "type": "function"}, {"inputs": [{"internalType": "address", "name": "spender", "type": "address"}, {"internalType": "uint256", "name": "amount", "type": "uint256"}], "name": "approve", "outputs": [{"internalType": "bool", "name": "", "type": "bool"}], "stateMutability": "nonpayable", "type": "function"}, {"inputs": [{"internalType": "address", "name": "account", "type": "address"}], "name": "balanceOf", "outputs": [{"internalType": "uint256", "name": "", "type": "uint256"}], "stateMutability": "view", "type": "function"}, {"inputs": [{"internalType": "address", "name": "from", "type": "address"}, {"internalType": "uint256", "name": "amount", "type": "uint256"}], "name": "burn", "outputs": [], "stateMutability": "nonpayable", "type": "function"}, {"inputs": [], "name": "decimals", "outputs": [{"internalType": "uint8", "name": "", "type": "uint8"}], "stateMutability": "view", "type": "function"}, {"inputs": [{"internalType": "address", "name": "spender", "type": "address"}, {"internalType": "uint256", "name": "subtractedValue", "type": "uint256"}], "name": "decreaseAllowance", "outputs": [{"internalType": "bool", "name": "", "type": "bool"}], "stateMutability": "nonpayable", "type": "function"}, {"inputs": [{"internalType": "address", "name": "spender", "type": "address"}, {"internalType": "uint256", "name": "addedValue", "type": "uint256"}], "name": "increaseAllowance", "outputs": [{"internalType": "bool", "name": "", "type": "bool"}], "stateMutability": "nonpayable", "type": "function"}, {"inputs": [{"internalType": "address", "name": "to", "type": "address"}, {"internalType": "uint256", "name": "amount", "type": "uint256"}], "name": "mint", "outputs": [], "stateMutability": "nonpayable", "type": "function"}, {"inputs": [{"internalType": "uint256", "name": "amount", "type": "uint256"}], "name": "mintToSelf", "outputs": [], "stateMutability": "nonpayable", "type": "function"}, {"inputs": [], "name": "name", "outputs": [{"internalType": "string", "name": "", "type": "string"}], "stateMutability": "view", "type": "function"}, {"inputs": [], "name": "owner", "outputs": [{"internalType": "address", "name": "", "type": "address"}], "stateMutability": "view", "type": "function"}, {"inputs": [], "name": "renounceOwnership", "outputs": [], "stateMutability": "nonpayable", "type": "function"}, {"inputs": [], "name": "symbol", "outputs": [{"internalType": "string", "name": "", "type": "string"}], "stateMutability": "view", "type": "function"}, {"inputs": [], "name": "totalSupply", "outputs": [{"internalType": "uint256", "name": "", "type": "uint256"}], "stateMutability": "view", "type": "function"}, {"inputs": [{"internalType": "address", "name": "to", "type": "address"}, {"internalType": "uint256", "name": "amount", "type": "uint256"}], "name": "transfer", "outputs": [{"internalType": "bool", "name": "", "type": "bool"}], "stateMutability": "nonpayable", "type": "function"}, {"inputs": [{"internalType": "address", "name": "from", "type": "address"}, {"internalType": "address", "name": "to", "type": "address"}, {"internalType": "uint256", "name": "amount", "type": "uint256"}], "name": "transferFrom", "outputs": [{"internalType": "bool", "name": "", "type": "bool"}], "stateMutability": "nonpayable", "type": "function"}, {"inputs": [{"internalType": "address", "name": "new<PERSON>wner", "type": "address"}], "name": "transferOwnership", "outputs": [], "stateMutability": "nonpayable", "type": "function"}], "bytecode": "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", "deployedBytecode": "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", "linkReferences": {}, "deployedLinkReferences": {}}