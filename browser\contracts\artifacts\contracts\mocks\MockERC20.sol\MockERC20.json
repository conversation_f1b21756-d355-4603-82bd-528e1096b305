{"_format": "hh-sol-artifact-1", "contractName": "MockERC20", "sourceName": "contracts/mocks/MockERC20.sol", "abi": [{"inputs": [{"internalType": "string", "name": "name", "type": "string"}, {"internalType": "string", "name": "symbol", "type": "string"}, {"internalType": "uint8", "name": "decimals_", "type": "uint8"}], "stateMutability": "nonpayable", "type": "constructor"}, {"anonymous": false, "inputs": [{"indexed": true, "internalType": "address", "name": "owner", "type": "address"}, {"indexed": true, "internalType": "address", "name": "spender", "type": "address"}, {"indexed": false, "internalType": "uint256", "name": "value", "type": "uint256"}], "name": "Approval", "type": "event"}, {"anonymous": false, "inputs": [{"indexed": true, "internalType": "address", "name": "previousOwner", "type": "address"}, {"indexed": true, "internalType": "address", "name": "new<PERSON>wner", "type": "address"}], "name": "OwnershipTransferred", "type": "event"}, {"anonymous": false, "inputs": [{"indexed": true, "internalType": "address", "name": "from", "type": "address"}, {"indexed": true, "internalType": "address", "name": "to", "type": "address"}, {"indexed": false, "internalType": "uint256", "name": "value", "type": "uint256"}], "name": "Transfer", "type": "event"}, {"inputs": [{"internalType": "address", "name": "owner", "type": "address"}, {"internalType": "address", "name": "spender", "type": "address"}], "name": "allowance", "outputs": [{"internalType": "uint256", "name": "", "type": "uint256"}], "stateMutability": "view", "type": "function"}, {"inputs": [{"internalType": "address", "name": "spender", "type": "address"}, {"internalType": "uint256", "name": "amount", "type": "uint256"}], "name": "approve", "outputs": [{"internalType": "bool", "name": "", "type": "bool"}], "stateMutability": "nonpayable", "type": "function"}, {"inputs": [{"internalType": "address", "name": "account", "type": "address"}], "name": "balanceOf", "outputs": [{"internalType": "uint256", "name": "", "type": "uint256"}], "stateMutability": "view", "type": "function"}, {"inputs": [{"internalType": "address", "name": "from", "type": "address"}, {"internalType": "uint256", "name": "amount", "type": "uint256"}], "name": "burn", "outputs": [], "stateMutability": "nonpayable", "type": "function"}, {"inputs": [], "name": "decimals", "outputs": [{"internalType": "uint8", "name": "", "type": "uint8"}], "stateMutability": "view", "type": "function"}, {"inputs": [{"internalType": "address", "name": "spender", "type": "address"}, {"internalType": "uint256", "name": "subtractedValue", "type": "uint256"}], "name": "decreaseAllowance", "outputs": [{"internalType": "bool", "name": "", "type": "bool"}], "stateMutability": "nonpayable", "type": "function"}, {"inputs": [{"internalType": "address", "name": "spender", "type": "address"}, {"internalType": "uint256", "name": "addedValue", "type": "uint256"}], "name": "increaseAllowance", "outputs": [{"internalType": "bool", "name": "", "type": "bool"}], "stateMutability": "nonpayable", "type": "function"}, {"inputs": [{"internalType": "address", "name": "to", "type": "address"}, {"internalType": "uint256", "name": "amount", "type": "uint256"}], "name": "mint", "outputs": [], "stateMutability": "nonpayable", "type": "function"}, {"inputs": [{"internalType": "uint256", "name": "amount", "type": "uint256"}], "name": "mintToSelf", "outputs": [], "stateMutability": "nonpayable", "type": "function"}, {"inputs": [], "name": "name", "outputs": [{"internalType": "string", "name": "", "type": "string"}], "stateMutability": "view", "type": "function"}, {"inputs": [], "name": "owner", "outputs": [{"internalType": "address", "name": "", "type": "address"}], "stateMutability": "view", "type": "function"}, {"inputs": [], "name": "renounceOwnership", "outputs": [], "stateMutability": "nonpayable", "type": "function"}, {"inputs": [], "name": "symbol", "outputs": [{"internalType": "string", "name": "", "type": "string"}], "stateMutability": "view", "type": "function"}, {"inputs": [], "name": "totalSupply", "outputs": [{"internalType": "uint256", "name": "", "type": "uint256"}], "stateMutability": "view", "type": "function"}, {"inputs": [{"internalType": "address", "name": "to", "type": "address"}, {"internalType": "uint256", "name": "amount", "type": "uint256"}], "name": "transfer", "outputs": [{"internalType": "bool", "name": "", "type": "bool"}], "stateMutability": "nonpayable", "type": "function"}, {"inputs": [{"internalType": "address", "name": "from", "type": "address"}, {"internalType": "address", "name": "to", "type": "address"}, {"internalType": "uint256", "name": "amount", "type": "uint256"}], "name": "transferFrom", "outputs": [{"internalType": "bool", "name": "", "type": "bool"}], "stateMutability": "nonpayable", "type": "function"}, {"inputs": [{"internalType": "address", "name": "new<PERSON>wner", "type": "address"}], "name": "transferOwnership", "outputs": [], "stateMutability": "nonpayable", "type": "function"}], "bytecode": "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", "deployedBytecode": "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", "linkReferences": {}, "deployedLinkReferences": {}}