{"_format": "hh-sol-artifact-1", "contractName": "AdminControl", "sourceName": "contracts/governance/AdminControl.sol", "abi": [{"inputs": [{"internalType": "address", "name": "initialAdmin", "type": "address"}, {"internalType": "address", "name": "feeCollector", "type": "address"}, {"internalType": "address", "name": "rewardsVault", "type": "address"}], "stateMutability": "nonpayable", "type": "constructor"}, {"anonymous": false, "inputs": [{"indexed": true, "internalType": "bytes32", "name": "role", "type": "bytes32"}, {"indexed": true, "internalType": "address", "name": "account", "type": "address"}, {"indexed": true, "internalType": "address", "name": "sender", "type": "address"}], "name": "AdminRoleGranted", "type": "event"}, {"anonymous": false, "inputs": [{"indexed": true, "internalType": "bytes32", "name": "role", "type": "bytes32"}, {"indexed": true, "internalType": "address", "name": "account", "type": "address"}, {"indexed": true, "internalType": "address", "name": "sender", "type": "address"}], "name": "AdminRoleRevoked", "type": "event"}, {"anonymous": false, "inputs": [{"indexed": true, "internalType": "address", "name": "user", "type": "address"}, {"indexed": false, "internalType": "uint256", "name": "oldScore", "type": "uint256"}, {"indexed": false, "internalType": "uint256", "name": "newScore", "type": "uint256"}], "name": "CommunityScoreUpdated", "type": "event"}, {"anonymous": false, "inputs": [{"indexed": false, "internalType": "uint256", "name": "oldBase<PERSON>ee", "type": "uint256"}, {"indexed": false, "internalType": "uint256", "name": "newBaseFee", "type": "uint256"}, {"indexed": false, "internalType": "uint256", "name": "oldMaxFee", "type": "uint256"}, {"indexed": false, "internalType": "uint256", "name": "newMaxFee", "type": "uint256"}, {"indexed": true, "internalType": "address", "name": "admin", "type": "address"}], "name": "FeeConfigUpdated", "type": "event"}, {"anonymous": false, "inputs": [{"indexed": true, "internalType": "address", "name": "account", "type": "address"}, {"indexed": false, "internalType": "bool", "name": "approved", "type": "bool"}], "name": "KYCStatusUpdated", "type": "event"}, {"anonymous": false, "inputs": [{"indexed": false, "internalType": "address", "name": "account", "type": "address"}], "name": "Paused", "type": "event"}, {"anonymous": false, "inputs": [{"indexed": false, "internalType": "uint256", "name": "oldBaseRate", "type": "uint256"}, {"indexed": false, "internalType": "uint256", "name": "newBaseRate", "type": "uint256"}, {"indexed": false, "internalType": "uint256", "name": "oldMultiplier", "type": "uint256"}, {"indexed": false, "internalType": "uint256", "name": "newMultiplier", "type": "uint256"}, {"indexed": true, "internalType": "address", "name": "admin", "type": "address"}], "name": "RewardParametersUpdated", "type": "event"}, {"anonymous": false, "inputs": [{"indexed": true, "internalType": "bytes32", "name": "role", "type": "bytes32"}, {"indexed": true, "internalType": "bytes32", "name": "previousAdminRole", "type": "bytes32"}, {"indexed": true, "internalType": "bytes32", "name": "newAdminRole", "type": "bytes32"}], "name": "RoleAdminChanged", "type": "event"}, {"anonymous": false, "inputs": [{"indexed": true, "internalType": "bytes32", "name": "role", "type": "bytes32"}, {"indexed": true, "internalType": "address", "name": "account", "type": "address"}, {"indexed": true, "internalType": "address", "name": "sender", "type": "address"}], "name": "RoleGranted", "type": "event"}, {"anonymous": false, "inputs": [{"indexed": true, "internalType": "bytes32", "name": "role", "type": "bytes32"}, {"indexed": true, "internalType": "address", "name": "account", "type": "address"}, {"indexed": true, "internalType": "address", "name": "sender", "type": "address"}], "name": "RoleRevoked", "type": "event"}, {"anonymous": false, "inputs": [{"indexed": false, "internalType": "address", "name": "account", "type": "address"}], "name": "Unpaused", "type": "event"}, {"inputs": [], "name": "BASIS_POINTS", "outputs": [{"internalType": "uint256", "name": "", "type": "uint256"}], "stateMutability": "view", "type": "function"}, {"inputs": [], "name": "DEFAULT_ADMIN_ROLE", "outputs": [{"internalType": "bytes32", "name": "", "type": "bytes32"}], "stateMutability": "view", "type": "function"}, {"inputs": [], "name": "LEGAL_ROLE", "outputs": [{"internalType": "bytes32", "name": "", "type": "bytes32"}], "stateMutability": "view", "type": "function"}, {"inputs": [], "name": "OPERATOR_ROLE", "outputs": [{"internalType": "bytes32", "name": "", "type": "bytes32"}], "stateMutability": "view", "type": "function"}, {"inputs": [], "name": "REWARD_MANAGER", "outputs": [{"internalType": "bytes32", "name": "", "type": "bytes32"}], "stateMutability": "view", "type": "function"}, {"inputs": [{"internalType": "address[]", "name": "accounts", "type": "address[]"}, {"internalType": "bool", "name": "approved", "type": "bool"}], "name": "batchApproveKYC", "outputs": [], "stateMutability": "nonpayable", "type": "function"}, {"inputs": [{"internalType": "address", "name": "user", "type": "address"}, {"internalType": "uint256", "name": "baseAmount", "type": "uint256"}], "name": "calculateRewards", "outputs": [{"internalType": "uint256", "name": "", "type": "uint256"}], "stateMutability": "view", "type": "function"}, {"inputs": [{"internalType": "address", "name": "", "type": "address"}], "name": "communityScores", "outputs": [{"internalType": "uint256", "name": "", "type": "uint256"}], "stateMutability": "view", "type": "function"}, {"inputs": [{"internalType": "uint256", "name": "newBaseRate", "type": "uint256"}, {"internalType": "uint256", "name": "newMultiplier", "type": "uint256"}, {"internalType": "uint256", "name": "newLeaseBonus", "type": "uint256"}], "name": "configureRewards", "outputs": [], "stateMutability": "nonpayable", "type": "function"}, {"inputs": [{"internalType": "uint256", "name": "functionId", "type": "uint256"}, {"internalType": "bool", "name": "paused", "type": "bool"}], "name": "emergencyPauseFunction", "outputs": [], "stateMutability": "nonpayable", "type": "function"}, {"inputs": [], "name": "feeConfig", "outputs": [{"internalType": "uint256", "name": "baseFee", "type": "uint256"}, {"internalType": "uint256", "name": "maxFee", "type": "uint256"}, {"internalType": "address", "name": "feeCollector", "type": "address"}], "stateMutability": "view", "type": "function"}, {"inputs": [{"internalType": "uint256", "name": "", "type": "uint256"}], "name": "functionPaused", "outputs": [{"internalType": "bool", "name": "", "type": "bool"}], "stateMutability": "view", "type": "function"}, {"inputs": [], "name": "getCurrentFee", "outputs": [{"internalType": "uint256", "name": "", "type": "uint256"}], "stateMutability": "view", "type": "function"}, {"inputs": [{"internalType": "bytes32", "name": "role", "type": "bytes32"}], "name": "getRoleAdmin", "outputs": [{"internalType": "bytes32", "name": "", "type": "bytes32"}], "stateMutability": "view", "type": "function"}, {"inputs": [], "name": "globalPause", "outputs": [], "stateMutability": "nonpayable", "type": "function"}, {"inputs": [], "name": "globalUnpause", "outputs": [], "stateMutability": "nonpayable", "type": "function"}, {"inputs": [{"internalType": "bytes32", "name": "role", "type": "bytes32"}, {"internalType": "address", "name": "account", "type": "address"}], "name": "grantRole", "outputs": [], "stateMutability": "nonpayable", "type": "function"}, {"inputs": [{"internalType": "bytes32", "name": "role", "type": "bytes32"}, {"internalType": "address", "name": "account", "type": "address"}], "name": "hasRole", "outputs": [{"internalType": "bool", "name": "", "type": "bool"}], "stateMutability": "view", "type": "function"}, {"inputs": [{"internalType": "address", "name": "account", "type": "address"}], "name": "isKYCVerified", "outputs": [{"internalType": "bool", "name": "", "type": "bool"}], "stateMutability": "view", "type": "function"}, {"inputs": [], "name": "paused", "outputs": [{"internalType": "bool", "name": "", "type": "bool"}], "stateMutability": "view", "type": "function"}, {"inputs": [{"internalType": "bytes32", "name": "role", "type": "bytes32"}, {"internalType": "address", "name": "account", "type": "address"}], "name": "renounceRole", "outputs": [], "stateMutability": "nonpayable", "type": "function"}, {"inputs": [{"internalType": "bytes32", "name": "role", "type": "bytes32"}, {"internalType": "address", "name": "account", "type": "address"}], "name": "revokeRole", "outputs": [], "stateMutability": "nonpayable", "type": "function"}, {"inputs": [], "name": "rewardParams", "outputs": [{"internalType": "uint256", "name": "baseRate", "type": "uint256"}, {"internalType": "uint256", "name": "communityMultiplier", "type": "uint256"}, {"internalType": "uint256", "name": "maxLeaseBonus", "type": "uint256"}, {"internalType": "address", "name": "rewardsVault", "type": "address"}], "stateMutability": "view", "type": "function"}, {"inputs": [{"internalType": "bytes4", "name": "interfaceId", "type": "bytes4"}], "name": "supportsInterface", "outputs": [{"internalType": "bool", "name": "", "type": "bool"}], "stateMutability": "view", "type": "function"}, {"inputs": [{"internalType": "address", "name": "user", "type": "address"}, {"internalType": "uint256", "name": "scoreDelta", "type": "uint256"}, {"internalType": "bool", "name": "isAddition", "type": "bool"}], "name": "updateCommunityScore", "outputs": [], "stateMutability": "nonpayable", "type": "function"}, {"inputs": [{"internalType": "uint256", "name": "newBaseFee", "type": "uint256"}, {"internalType": "address", "name": "newCollector", "type": "address"}], "name": "updateFeeConfig", "outputs": [], "stateMutability": "nonpayable", "type": "function"}], "bytecode": "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", "deployedBytecode": "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", "linkReferences": {}, "deployedLinkReferences": {}}