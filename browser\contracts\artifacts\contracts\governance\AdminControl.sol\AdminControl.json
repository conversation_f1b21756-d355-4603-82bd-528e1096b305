{"_format": "hh-sol-artifact-1", "contractName": "AdminControl", "sourceName": "contracts/governance/AdminControl.sol", "abi": [{"inputs": [{"internalType": "address", "name": "initialAdmin", "type": "address"}, {"internalType": "address", "name": "feeCollector", "type": "address"}, {"internalType": "address", "name": "rewardsVault", "type": "address"}], "stateMutability": "nonpayable", "type": "constructor"}, {"anonymous": false, "inputs": [{"indexed": true, "internalType": "bytes32", "name": "role", "type": "bytes32"}, {"indexed": true, "internalType": "address", "name": "account", "type": "address"}, {"indexed": true, "internalType": "address", "name": "sender", "type": "address"}], "name": "AdminRoleGranted", "type": "event"}, {"anonymous": false, "inputs": [{"indexed": true, "internalType": "bytes32", "name": "role", "type": "bytes32"}, {"indexed": true, "internalType": "address", "name": "account", "type": "address"}, {"indexed": true, "internalType": "address", "name": "sender", "type": "address"}], "name": "AdminRoleRevoked", "type": "event"}, {"anonymous": false, "inputs": [{"indexed": true, "internalType": "address", "name": "user", "type": "address"}, {"indexed": false, "internalType": "uint256", "name": "oldScore", "type": "uint256"}, {"indexed": false, "internalType": "uint256", "name": "newScore", "type": "uint256"}], "name": "CommunityScoreUpdated", "type": "event"}, {"anonymous": false, "inputs": [{"indexed": false, "internalType": "uint256", "name": "oldBase<PERSON>ee", "type": "uint256"}, {"indexed": false, "internalType": "uint256", "name": "newBaseFee", "type": "uint256"}, {"indexed": false, "internalType": "uint256", "name": "oldMaxFee", "type": "uint256"}, {"indexed": false, "internalType": "uint256", "name": "newMaxFee", "type": "uint256"}, {"indexed": true, "internalType": "address", "name": "admin", "type": "address"}], "name": "FeeConfigUpdated", "type": "event"}, {"anonymous": false, "inputs": [{"indexed": true, "internalType": "address", "name": "account", "type": "address"}, {"indexed": false, "internalType": "bool", "name": "approved", "type": "bool"}], "name": "KYCStatusUpdated", "type": "event"}, {"anonymous": false, "inputs": [{"indexed": false, "internalType": "address", "name": "account", "type": "address"}], "name": "Paused", "type": "event"}, {"anonymous": false, "inputs": [{"indexed": false, "internalType": "uint256", "name": "oldBaseRate", "type": "uint256"}, {"indexed": false, "internalType": "uint256", "name": "newBaseRate", "type": "uint256"}, {"indexed": false, "internalType": "uint256", "name": "oldMultiplier", "type": "uint256"}, {"indexed": false, "internalType": "uint256", "name": "newMultiplier", "type": "uint256"}, {"indexed": true, "internalType": "address", "name": "admin", "type": "address"}], "name": "RewardParametersUpdated", "type": "event"}, {"anonymous": false, "inputs": [{"indexed": true, "internalType": "bytes32", "name": "role", "type": "bytes32"}, {"indexed": true, "internalType": "bytes32", "name": "previousAdminRole", "type": "bytes32"}, {"indexed": true, "internalType": "bytes32", "name": "newAdminRole", "type": "bytes32"}], "name": "RoleAdminChanged", "type": "event"}, {"anonymous": false, "inputs": [{"indexed": true, "internalType": "bytes32", "name": "role", "type": "bytes32"}, {"indexed": true, "internalType": "address", "name": "account", "type": "address"}, {"indexed": true, "internalType": "address", "name": "sender", "type": "address"}], "name": "RoleGranted", "type": "event"}, {"anonymous": false, "inputs": [{"indexed": true, "internalType": "bytes32", "name": "role", "type": "bytes32"}, {"indexed": true, "internalType": "address", "name": "account", "type": "address"}, {"indexed": true, "internalType": "address", "name": "sender", "type": "address"}], "name": "RoleRevoked", "type": "event"}, {"anonymous": false, "inputs": [{"indexed": false, "internalType": "address", "name": "account", "type": "address"}], "name": "Unpaused", "type": "event"}, {"inputs": [], "name": "BASIS_POINTS", "outputs": [{"internalType": "uint256", "name": "", "type": "uint256"}], "stateMutability": "view", "type": "function"}, {"inputs": [], "name": "DEFAULT_ADMIN_ROLE", "outputs": [{"internalType": "bytes32", "name": "", "type": "bytes32"}], "stateMutability": "view", "type": "function"}, {"inputs": [], "name": "LEGAL_ROLE", "outputs": [{"internalType": "bytes32", "name": "", "type": "bytes32"}], "stateMutability": "view", "type": "function"}, {"inputs": [], "name": "OPERATOR_ROLE", "outputs": [{"internalType": "bytes32", "name": "", "type": "bytes32"}], "stateMutability": "view", "type": "function"}, {"inputs": [], "name": "REWARD_MANAGER", "outputs": [{"internalType": "bytes32", "name": "", "type": "bytes32"}], "stateMutability": "view", "type": "function"}, {"inputs": [{"internalType": "address[]", "name": "accounts", "type": "address[]"}, {"internalType": "bool", "name": "approved", "type": "bool"}], "name": "batchApproveKYC", "outputs": [], "stateMutability": "nonpayable", "type": "function"}, {"inputs": [{"internalType": "address", "name": "user", "type": "address"}, {"internalType": "uint256", "name": "baseAmount", "type": "uint256"}], "name": "calculateRewards", "outputs": [{"internalType": "uint256", "name": "", "type": "uint256"}], "stateMutability": "view", "type": "function"}, {"inputs": [{"internalType": "address", "name": "", "type": "address"}], "name": "communityScores", "outputs": [{"internalType": "uint256", "name": "", "type": "uint256"}], "stateMutability": "view", "type": "function"}, {"inputs": [{"internalType": "uint256", "name": "newBaseRate", "type": "uint256"}, {"internalType": "uint256", "name": "newMultiplier", "type": "uint256"}, {"internalType": "uint256", "name": "newLeaseBonus", "type": "uint256"}], "name": "configureRewards", "outputs": [], "stateMutability": "nonpayable", "type": "function"}, {"inputs": [{"internalType": "uint256", "name": "functionId", "type": "uint256"}, {"internalType": "bool", "name": "paused", "type": "bool"}], "name": "emergencyPauseFunction", "outputs": [], "stateMutability": "nonpayable", "type": "function"}, {"inputs": [], "name": "feeConfig", "outputs": [{"internalType": "uint256", "name": "baseFee", "type": "uint256"}, {"internalType": "uint256", "name": "maxFee", "type": "uint256"}, {"internalType": "address", "name": "feeCollector", "type": "address"}], "stateMutability": "view", "type": "function"}, {"inputs": [{"internalType": "uint256", "name": "", "type": "uint256"}], "name": "functionPaused", "outputs": [{"internalType": "bool", "name": "", "type": "bool"}], "stateMutability": "view", "type": "function"}, {"inputs": [], "name": "getCurrentFee", "outputs": [{"internalType": "uint256", "name": "", "type": "uint256"}], "stateMutability": "view", "type": "function"}, {"inputs": [{"internalType": "bytes32", "name": "role", "type": "bytes32"}], "name": "getRoleAdmin", "outputs": [{"internalType": "bytes32", "name": "", "type": "bytes32"}], "stateMutability": "view", "type": "function"}, {"inputs": [], "name": "globalPause", "outputs": [], "stateMutability": "nonpayable", "type": "function"}, {"inputs": [], "name": "globalUnpause", "outputs": [], "stateMutability": "nonpayable", "type": "function"}, {"inputs": [{"internalType": "bytes32", "name": "role", "type": "bytes32"}, {"internalType": "address", "name": "account", "type": "address"}], "name": "grantRole", "outputs": [], "stateMutability": "nonpayable", "type": "function"}, {"inputs": [{"internalType": "bytes32", "name": "role", "type": "bytes32"}, {"internalType": "address", "name": "account", "type": "address"}], "name": "hasRole", "outputs": [{"internalType": "bool", "name": "", "type": "bool"}], "stateMutability": "view", "type": "function"}, {"inputs": [{"internalType": "address", "name": "account", "type": "address"}], "name": "isKYCVerified", "outputs": [{"internalType": "bool", "name": "", "type": "bool"}], "stateMutability": "view", "type": "function"}, {"inputs": [], "name": "paused", "outputs": [{"internalType": "bool", "name": "", "type": "bool"}], "stateMutability": "view", "type": "function"}, {"inputs": [{"internalType": "bytes32", "name": "role", "type": "bytes32"}, {"internalType": "address", "name": "account", "type": "address"}], "name": "renounceRole", "outputs": [], "stateMutability": "nonpayable", "type": "function"}, {"inputs": [{"internalType": "bytes32", "name": "role", "type": "bytes32"}, {"internalType": "address", "name": "account", "type": "address"}], "name": "revokeRole", "outputs": [], "stateMutability": "nonpayable", "type": "function"}, {"inputs": [], "name": "rewardParams", "outputs": [{"internalType": "uint256", "name": "baseRate", "type": "uint256"}, {"internalType": "uint256", "name": "communityMultiplier", "type": "uint256"}, {"internalType": "uint256", "name": "maxLeaseBonus", "type": "uint256"}, {"internalType": "address", "name": "rewardsVault", "type": "address"}], "stateMutability": "view", "type": "function"}, {"inputs": [{"internalType": "bytes4", "name": "interfaceId", "type": "bytes4"}], "name": "supportsInterface", "outputs": [{"internalType": "bool", "name": "", "type": "bool"}], "stateMutability": "view", "type": "function"}, {"inputs": [{"internalType": "address", "name": "user", "type": "address"}, {"internalType": "uint256", "name": "scoreDelta", "type": "uint256"}, {"internalType": "bool", "name": "isAddition", "type": "bool"}], "name": "updateCommunityScore", "outputs": [], "stateMutability": "nonpayable", "type": "function"}, {"inputs": [{"internalType": "uint256", "name": "newBaseFee", "type": "uint256"}, {"internalType": "address", "name": "newCollector", "type": "address"}], "name": "updateFeeConfig", "outputs": [], "stateMutability": "nonpayable", "type": "function"}], "bytecode": "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", "deployedBytecode": "0x608060405234801561001057600080fd5b506004361061013e5760003560e01c806301ffc9a7146101435780631e5eb1d01461016b578063248a9ca3146101aa5780632f2ff15d146101cb57806336568abe146101e05780634a277ec5146101f357806354b7636e146102065780635932fdba146102195780635c975abb1461022e5780635dda3e011461023957806373c250c61461024c578063763469141461026c57806388f0bc73146102b45780638e7579a1146102c757806391d14854146102dc578063a217fddf146102ef578063a33b2d3f146102f7578063beb8314c1461030a578063ce1905781461031d578063cf23913b14610325578063d547741f14610338578063e1f1c4a71461034b578063ee5b8e0e14610354578063f12d54d814610377578063f5b541a61461037f578063f70d936214610394575b600080fd5b610156610151366004611053565b61039c565b60405190151581526020015b60405180910390f35b6002546003546004546101869291906001600160a01b031683565b6040805193845260208401929092526001600160a01b031690820152606001610162565b6101bd6101b836600461107d565b6103d3565b604051908152602001610162565b6101de6101d93660046110b2565b6103e8565b005b6101de6101ee3660046110b2565b610441565b6101de6102013660046110de565b6104c4565b61015661021436600461110a565b610656565b6101bd6000805160206113e883398151915281565b60015460ff16610156565b6101de610247366004611135565b610663565b6101bd61025a36600461110a565b600b6020526000908152604090205481565b60055460065460075460085461028b939291906001600160a01b031684565b604080519485526020850193909352918301526001600160a01b03166060820152608001610162565b6101de6102c23660046110b2565b61072c565b6101bd60008051602061142883398151915281565b6101566102ea3660046110b2565b61084e565b6101bd600081565b6101de6103053660046111b8565b610877565b6101bd6103183660046111eb565b61092d565b6101de610984565b6101de610333366004611215565b61099a565b6101de6103463660046110b2565b6109c6565b6101bd61271081565b61015661036236600461107d565b600c6020526000908152604090205460ff1681565b6101de610a1f565b6101bd60008051602061140883398151915281565b6002546101bd565b60006001600160e01b03198216637965db0b60e01b14806103cd57506301ffc9a760e01b6001600160e01b03198316145b92915050565b60009081526020819052604090206001015490565b6103f1826103d3565b6103fa81610a32565b6104048383610a3c565b60405133906001600160a01b0384169085907fb754d28367d5b086c81243c886ead0839c92cb5f281e492d7159800bc57d3c1790600090a4505050565b6001600160a01b03811633146104b65760405162461bcd60e51b815260206004820152602f60248201527f416363657373436f6e74726f6c3a2063616e206f6e6c792072656e6f756e636560448201526e103937b632b9903337b91039b2b63360891b60648201526084015b60405180910390fd5b6104c08282610a5d565b5050565b6000805160206113e88339815191526104dc81610a32565b6008546001600160a01b031661052c5760405162461bcd60e51b815260206004820152601560248201527414995dd85c991cc81d985d5b1d081b9bdd081cd95d605a1b60448201526064016104ad565b6107d084111561056f5760405162461bcd60e51b815260206004820152600e60248201526d426173652072617465203e32302560901b60448201526064016104ad565b6107d08311156105b35760405162461bcd60e51b815260206004820152600f60248201526e4d756c7469706c696572203e32302560881b60448201526064016104ad565b6101f48211156105f75760405162461bcd60e51b815260206004820152600f60248201526e4c6561736520626f6e7573203e352560881b60448201526064016104ad565b600580546006805492879055859055600784905560405190919033907fdf3827857cf8ac45829164bd76e2950687437e15ff8543f477ae73691ee013b1906106469085908a9086908b90611238565b60405180910390a2505050505050565b60006103cd600983610ac2565b60008051602061142883398151915261067b81610a32565b8260005b8181101561072457600086868381811061069b5761069b611253565b90506020020160208101906106b0919061110a565b905084156106c9576106c3600982610ade565b506106d6565b6106d4600982610af3565b505b806001600160a01b03167f2df1cad2a2659e001c3804c872f69655356013e35c18c2e77a5d568078ab788f86604051610713911515815260200190565b60405180910390a25060010161067f565b505050505050565b60008051602061140883398151915261074481610a32565b6001600160a01b03821661079a5760405162461bcd60e51b815260206004820152601d60248201527f496e76616c69642066656520636f6c6c6563746f72206164647265737300000060448201526064016104ad565b6003548311156107de5760405162461bcd60e51b815260206004820152600f60248201526e45786365656473206d61782066656560881b60448201526064016104ad565b6002805460035491859055600480546001600160a01b0319166001600160a01b03861617905560405190919033907f7c08ec61bfdc4469c5f8ec2332c513ac1e0980475856efedf21da749334854479061083f908590899086908190611238565b60405180910390a25050505050565b6000918252602082815260408084206001600160a01b0393909316845291905290205460ff1690565b60008051602061140883398151915261088f81610a32565b6001600160a01b0384166000908152600b60205260408120549083156108c0576108b9858361127f565b90506108db565b8482116108ce5760006108d8565b6108d88583611292565b90505b6001600160a01b0386166000818152600b602090815260409182902084905581518581529081018490527f7681c9bc4703daadfdfbfae0bd1c828b4619e58101f0e8a5404f70e93c6286639101610646565b60008061093960075490565b9050600061094685610b08565b9050612710818360056000015461095d919061127f565b610967919061127f565b61097190866112a5565b61097b91906112bc565b95945050505050565b600061098f81610a32565b610997610b3a565b50565b60006109a581610a32565b506000918252600c6020526040909120805460ff1916911515919091179055565b6109cf826103d3565b6109d881610a32565b6109e28383610b8c565b60405133906001600160a01b0384169085907f77a3124d5a7fb118ef3bd073fd7ee2c762b24256db96ace524ecd6b6cf11415c90600090a4505050565b6000610a2a81610a32565b610997610ba8565b6109978133610be3565b610a45826103d3565b610a4e81610a32565b610a588383610c3c565b505050565b610a67828261084e565b156104c0576000828152602081815260408083206001600160a01b0385168085529252808320805460ff1916905551339285917ff6391f5c32d9c69d2a47ea670b442974b53935d1edc7fd64eb21e047a839171b9190a45050565b6000610ad7836001600160a01b038416610cc0565b9392505050565b6000610ad7836001600160a01b038416610cd8565b6000610ad7836001600160a01b038416610d22565b6001600160a01b0381166000908152600b60205260408120546006548111610b305780610ad7565b6006549392505050565b610b42610e15565b6001805460ff191690557f5db9ee0a495bf2e6ff9c91a7834c1ba4fdd244a5e8aa4e537bd38aeae4b073aa335b6040516001600160a01b03909116815260200160405180910390a1565b610b95826103d3565b610b9e81610a32565b610a588383610a5d565b610bb0610e60565b6001805460ff1916811790557f62e78cea01bee320cd4e420270b5ea74000d11b0c9f74754ebdbfc544b05a25833610b6f565b610bed828261084e565b6104c057610bfa81610ea6565b610c05836020610eb8565b604051602001610c16929190611302565b60408051601f198184030181529082905262461bcd60e51b82526104ad91600401611371565b610c46828261084e565b6104c0576000828152602081815260408083206001600160a01b03851684529091529020805460ff19166001179055610c7c3390565b6001600160a01b0316816001600160a01b0316837f2f8788117e7eff1d82e926ec794901d17c78024a50270940304540a733656f0d60405160405180910390a45050565b60009081526001919091016020526040902054151590565b6000610ce48383610cc0565b610d1a575081546001818101845560008481526020808220909301849055845484825282860190935260409020919091556103cd565b5060006103cd565b60008181526001830160205260408120548015610e0b576000610d46600183611292565b8554909150600090610d5a90600190611292565b9050818114610dbf576000866000018281548110610d7a57610d7a611253565b9060005260206000200154905080876000018481548110610d9d57610d9d611253565b6000918252602080832090910192909255918252600188019052604090208390555b8554869080610dd057610dd06113a4565b6001900381819060005260206000200160009055905585600101600086815260200190815260200160002060009055600193505050506103cd565b60009150506103cd565b60015460ff16610e5e5760405162461bcd60e51b815260206004820152601460248201527314185d5cd8589b194e881b9bdd081c185d5cd95960621b60448201526064016104ad565b565b60015460ff1615610e5e5760405162461bcd60e51b815260206004820152601060248201526f14185d5cd8589b194e881c185d5cd95960821b60448201526064016104ad565b60606103cd6001600160a01b03831660145b60606000610ec78360026112a5565b610ed290600261127f565b6001600160401b03811115610ee957610ee96113ba565b6040519080825280601f01601f191660200182016040528015610f13576020820181803683370190505b509050600360fc1b81600081518110610f2e57610f2e611253565b60200101906001600160f81b031916908160001a905350600f60fb1b81600181518110610f5d57610f5d611253565b60200101906001600160f81b031916908160001a9053506000610f818460026112a5565b610f8c90600161127f565b90505b6001811115611004576f181899199a1a9b1b9c1cb0b131b232b360811b85600f1660108110610fc057610fc0611253565b1a60f81b828281518110610fd657610fd6611253565b60200101906001600160f81b031916908160001a90535060049490941c93610ffd816113d0565b9050610f8f565b508315610ad75760405162461bcd60e51b815260206004820181905260248201527f537472696e67733a20686578206c656e67746820696e73756666696369656e7460448201526064016104ad565b60006020828403121561106557600080fd5b81356001600160e01b031981168114610ad757600080fd5b60006020828403121561108f57600080fd5b5035919050565b80356001600160a01b03811681146110ad57600080fd5b919050565b600080604083850312156110c557600080fd5b823591506110d560208401611096565b90509250929050565b6000806000606084860312156110f357600080fd5b505081359360208301359350604090920135919050565b60006020828403121561111c57600080fd5b610ad782611096565b803580151581146110ad57600080fd5b60008060006040848603121561114a57600080fd5b83356001600160401b038082111561116157600080fd5b818601915086601f83011261117557600080fd5b81358181111561118457600080fd5b8760208260051b850101111561119957600080fd5b6020928301955093506111af9186019050611125565b90509250925092565b6000806000606084860312156111cd57600080fd5b6111d684611096565b9250602084013591506111af60408501611125565b600080604083850312156111fe57600080fd5b61120783611096565b946020939093013593505050565b6000806040838503121561122857600080fd5b823591506110d560208401611125565b93845260208401929092526040830152606082015260800190565b634e487b7160e01b600052603260045260246000fd5b634e487b7160e01b600052601160045260246000fd5b808201808211156103cd576103cd611269565b818103818111156103cd576103cd611269565b80820281158282048414176103cd576103cd611269565b6000826112d957634e487b7160e01b600052601260045260246000fd5b500490565b60005b838110156112f95781810151838201526020016112e1565b50506000910152565b76020b1b1b2b9b9a1b7b73a3937b61d1030b1b1b7bab73a1604d1b8152600083516113348160178501602088016112de565b7001034b99036b4b9b9b4b733903937b6329607d1b60179184019182015283516113658160288401602088016112de565b01602801949350505050565b60208152600082518060208401526113908160408501602087016112de565b601f01601f19169190910160400192915050565b634e487b7160e01b600052603160045260246000fd5b634e487b7160e01b600052604160045260246000fd5b6000816113df576113df611269565b50600019019056fe6393fa734211156fcc8c7dda6b0650f5c731d4c838c1db43445a149aa2f5b6fe97667070c54ef182b0f5858b034beac1b6f3089aa2d3188bb1e8929f4fa9b929b9f13ecb5e7f0f859c44b76b3a163e504787b446da95a26bf75e53e1ff4a1e0ea2646970667358221220bc069d38c0ed7d48332a0deb032e1e54fdb90c6cb12cbeb9b927d91ed7d6bb7164736f6c63430008140033", "linkReferences": {}, "deployedLinkReferences": {}}