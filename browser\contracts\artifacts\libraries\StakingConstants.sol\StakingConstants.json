{"_format": "hh-sol-artifact-1", "contractName": "StakingConstants", "sourceName": "libraries/StakingConstants.sol", "abi": [{"inputs": [], "name": "MIN_STAKING_PERIOD", "outputs": [{"internalType": "uint256", "name": "", "type": "uint256"}], "stateMutability": "view", "type": "function"}], "bytecode": "0x6089610038600b82828239805160001a607314602b57634e487b7160e01b600052600060045260246000fd5b30600052607381538281f3fe730000000000000000000000000000000000000000301460806040526004361060335760003560e01c8063c743dabb146038575b600080fd5b604162093a8081565b60405190815260200160405180910390f3fea2646970667358221220abb60154d950e854fe72c1ca1135d0106972a8fec2ac0660924c3d11ac421af964736f6c63430008140033", "deployedBytecode": "0x730000000000000000000000000000000000000000301460806040526004361060335760003560e01c8063c743dabb146038575b600080fd5b604162093a8081565b60405190815260200160405180910390f3fea2646970667358221220abb60154d950e854fe72c1ca1135d0106972a8fec2ac0660924c3d11ac421af964736f6c63430008140033", "linkReferences": {}, "deployedLinkReferences": {}}