{"_format": "hh-sol-artifact-1", "contractName": "StakingConstants", "sourceName": "libraries/StakingConstants.sol", "abi": [{"inputs": [], "name": "MIN_STAKING_PERIOD", "outputs": [{"internalType": "uint256", "name": "", "type": "uint256"}], "stateMutability": "view", "type": "function"}], "bytecode": "0x6089610038600b82828239805160001a607314602b57634e487b7160e01b600052600060045260246000fd5b30600052607381538281f3fe730000000000000000000000000000000000000000301460806040526004361060335760003560e01c8063c743dabb146038575b600080fd5b604162093a8081565b60405190815260200160405180910390f3fea2646970667358221220d10f9ec9c036637ec28f9343d65781395bacf77c4631b2623e966805e75ca2c864736f6c63430008140033", "deployedBytecode": "0x730000000000000000000000000000000000000000301460806040526004361060335760003560e01c8063c743dabb146038575b600080fd5b604162093a8081565b60405190815260200160405180910390f3fea2646970667358221220d10f9ec9c036637ec28f9343d65781395bacf77c4631b2623e966805e75ca2c864736f6c63430008140033", "linkReferences": {}, "deployedLinkReferences": {}}