{"_format": "hh-sol-artifact-1", "contractName": "LifeToken", "sourceName": "contracts/tokens/LifeToken.sol", "abi": [{"inputs": [{"internalType": "address", "name": "initialOwner_", "type": "address"}], "stateMutability": "nonpayable", "type": "constructor"}, {"anonymous": false, "inputs": [{"indexed": true, "internalType": "address", "name": "owner", "type": "address"}, {"indexed": true, "internalType": "address", "name": "spender", "type": "address"}, {"indexed": false, "internalType": "uint256", "name": "value", "type": "uint256"}], "name": "Approval", "type": "event"}, {"anonymous": false, "inputs": [{"indexed": true, "internalType": "address", "name": "account", "type": "address"}, {"indexed": false, "internalType": "uint256", "name": "oldAmount", "type": "uint256"}, {"indexed": false, "internalType": "uint256", "name": "newAmount", "type": "uint256"}, {"indexed": false, "internalType": "uint256", "name": "timestamp", "type": "uint256"}], "name": "BaseBalanceAdjusted", "type": "event"}, {"anonymous": false, "inputs": [{"indexed": true, "internalType": "address", "name": "newDistributor", "type": "address"}], "name": "DistributorUpdated", "type": "event"}, {"anonymous": false, "inputs": [{"indexed": true, "internalType": "uint256", "name": "epoch", "type": "uint256"}, {"indexed": false, "internalType": "uint256", "name": "oldFactor", "type": "uint256"}, {"indexed": false, "internalType": "uint256", "name": "newFactor", "type": "uint256"}, {"indexed": true, "internalType": "address", "name": "caller", "type": "address"}], "name": "EmergencyRebase", "type": "event"}, {"anonymous": false, "inputs": [{"indexed": true, "internalType": "address", "name": "account", "type": "address"}, {"indexed": false, "internalType": "bool", "name": "oldExcluded", "type": "bool"}, {"indexed": false, "internalType": "bool", "name": "newExcluded", "type": "bool"}], "name": "ExcludedFromRebase", "type": "event"}, {"anonymous": false, "inputs": [{"indexed": true, "internalType": "address", "name": "recipient", "type": "address"}, {"indexed": false, "internalType": "uint256", "name": "amount", "type": "uint256"}], "name": "Mint", "type": "event"}, {"anonymous": false, "inputs": [{"indexed": true, "internalType": "address", "name": "canceled<PERSON>wner", "type": "address"}], "name": "OwnershipTransferCanceled", "type": "event"}, {"anonymous": false, "inputs": [{"indexed": true, "internalType": "address", "name": "pending<PERSON><PERSON>er", "type": "address"}], "name": "OwnershipTransferStarted", "type": "event"}, {"anonymous": false, "inputs": [{"indexed": true, "internalType": "address", "name": "previousOwner", "type": "address"}, {"indexed": true, "internalType": "address", "name": "new<PERSON>wner", "type": "address"}], "name": "OwnershipTransferred", "type": "event"}, {"anonymous": false, "inputs": [{"indexed": true, "internalType": "uint256", "name": "epoch", "type": "uint256"}, {"indexed": false, "internalType": "uint256", "name": "oldFactor", "type": "uint256"}, {"indexed": false, "internalType": "uint256", "name": "newFactor", "type": "uint256"}], "name": "Rebase", "type": "event"}, {"anonymous": false, "inputs": [{"indexed": true, "internalType": "address", "name": "newRebaser", "type": "address"}], "name": "RebaserUpdated", "type": "event"}, {"anonymous": false, "inputs": [{"indexed": true, "internalType": "address", "name": "from", "type": "address"}, {"indexed": true, "internalType": "address", "name": "to", "type": "address"}, {"indexed": false, "internalType": "uint256", "name": "value", "type": "uint256"}], "name": "Transfer", "type": "event"}, {"inputs": [], "name": "INITIAL_SUPPLY", "outputs": [{"internalType": "uint256", "name": "", "type": "uint256"}], "stateMutability": "view", "type": "function"}, {"inputs": [], "name": "MAX_REBASE_FACTOR", "outputs": [{"internalType": "uint256", "name": "", "type": "uint256"}], "stateMutability": "view", "type": "function"}, {"inputs": [], "name": "MAX_SUPPLY", "outputs": [{"internalType": "uint256", "name": "", "type": "uint256"}], "stateMutability": "view", "type": "function"}, {"inputs": [], "name": "MIN_SUPPLY", "outputs": [{"internalType": "uint256", "name": "", "type": "uint256"}], "stateMutability": "view", "type": "function"}, {"inputs": [], "name": "REBASE_FACTOR_PRECISION", "outputs": [{"internalType": "uint256", "name": "", "type": "uint256"}], "stateMutability": "view", "type": "function"}, {"inputs": [], "name": "TOKEN_UNIT", "outputs": [{"internalType": "uint256", "name": "", "type": "uint256"}], "stateMutability": "view", "type": "function"}, {"inputs": [], "name": "acceptOwnership", "outputs": [], "stateMutability": "nonpayable", "type": "function"}, {"inputs": [{"internalType": "address", "name": "owner", "type": "address"}, {"internalType": "address", "name": "spender", "type": "address"}], "name": "allowance", "outputs": [{"internalType": "uint256", "name": "", "type": "uint256"}], "stateMutability": "view", "type": "function"}, {"inputs": [{"internalType": "address", "name": "spender", "type": "address"}, {"internalType": "uint256", "name": "amount", "type": "uint256"}], "name": "approve", "outputs": [{"internalType": "bool", "name": "", "type": "bool"}], "stateMutability": "nonpayable", "type": "function"}, {"inputs": [{"internalType": "address", "name": "account", "type": "address"}], "name": "balanceOf", "outputs": [{"internalType": "uint256", "name": "", "type": "uint256"}], "stateMutability": "view", "type": "function"}, {"inputs": [], "name": "cancelOwnershipTransfer", "outputs": [], "stateMutability": "nonpayable", "type": "function"}, {"inputs": [], "name": "decimals", "outputs": [{"internalType": "uint8", "name": "", "type": "uint8"}], "stateMutability": "view", "type": "function"}, {"inputs": [{"internalType": "address", "name": "spender", "type": "address"}, {"internalType": "uint256", "name": "subtractedValue", "type": "uint256"}], "name": "decreaseAllowance", "outputs": [{"internalType": "bool", "name": "", "type": "bool"}], "stateMutability": "nonpayable", "type": "function"}, {"inputs": [], "name": "distributor", "outputs": [{"internalType": "address", "name": "", "type": "address"}], "stateMutability": "view", "type": "function"}, {"inputs": [{"internalType": "uint256", "name": "newRebaseFactor", "type": "uint256"}], "name": "emergencyRebase", "outputs": [], "stateMutability": "nonpayable", "type": "function"}, {"inputs": [{"internalType": "address", "name": "account_", "type": "address"}], "name": "excludeFromRebase", "outputs": [], "stateMutability": "nonpayable", "type": "function"}, {"inputs": [{"internalType": "address", "name": "account", "type": "address"}], "name": "getBaseBalanceHistory", "outputs": [{"components": [{"internalType": "uint256", "name": "amount", "type": "uint256"}, {"internalType": "uint256", "name": "timestamp", "type": "uint256"}], "internalType": "struct LifeToken.BaseBalanceRecord[]", "name": "", "type": "tuple[]"}], "stateMutability": "view", "type": "function"}, {"inputs": [], "name": "getExcludedFromRebaseTotal", "outputs": [{"internalType": "uint256", "name": "", "type": "uint256"}], "stateMutability": "view", "type": "function"}, {"inputs": [{"internalType": "address", "name": "account", "type": "address"}], "name": "includeInRebase", "outputs": [], "stateMutability": "nonpayable", "type": "function"}, {"inputs": [{"internalType": "address", "name": "spender", "type": "address"}, {"internalType": "uint256", "name": "addedValue", "type": "uint256"}], "name": "increaseAllowance", "outputs": [{"internalType": "bool", "name": "", "type": "bool"}], "stateMutability": "nonpayable", "type": "function"}, {"inputs": [], "name": "initialDistributionDone", "outputs": [{"internalType": "bool", "name": "", "type": "bool"}], "stateMutability": "view", "type": "function"}, {"inputs": [{"internalType": "address", "name": "account", "type": "address"}], "name": "isExcludedFromRebase", "outputs": [{"internalType": "bool", "name": "", "type": "bool"}], "stateMutability": "view", "type": "function"}, {"inputs": [{"internalType": "address", "name": "recipient", "type": "address"}], "name": "mintRemainingSupply", "outputs": [], "stateMutability": "nonpayable", "type": "function"}, {"inputs": [], "name": "name", "outputs": [{"internalType": "string", "name": "", "type": "string"}], "stateMutability": "view", "type": "function"}, {"inputs": [], "name": "owner", "outputs": [{"internalType": "address", "name": "", "type": "address"}], "stateMutability": "view", "type": "function"}, {"inputs": [], "name": "ownershipTransferTime", "outputs": [{"internalType": "uint256", "name": "", "type": "uint256"}], "stateMutability": "view", "type": "function"}, {"inputs": [{"internalType": "uint256", "name": "newRebaseFactor", "type": "uint256"}], "name": "rebase", "outputs": [], "stateMutability": "nonpayable", "type": "function"}, {"inputs": [], "name": "rebaseConfig", "outputs": [{"internalType": "uint256", "name": "lastRebaseTimestamp", "type": "uint256"}, {"internalType": "uint256", "name": "minRebaseInterval", "type": "uint256"}, {"internalType": "uint256", "name": "rebaseFactor", "type": "uint256"}, {"internalType": "uint256", "name": "epoch", "type": "uint256"}], "stateMutability": "view", "type": "function"}, {"inputs": [], "name": "rebaseEpoch", "outputs": [{"internalType": "uint256", "name": "", "type": "uint256"}], "stateMutability": "view", "type": "function"}, {"inputs": [], "name": "rebaser", "outputs": [{"internalType": "address", "name": "", "type": "address"}], "stateMutability": "view", "type": "function"}, {"inputs": [], "name": "renounceOwnership", "outputs": [], "stateMutability": "view", "type": "function"}, {"inputs": [{"internalType": "address", "name": "newDistributor_", "type": "address"}], "name": "setDistributor", "outputs": [], "stateMutability": "nonpayable", "type": "function"}, {"inputs": [{"internalType": "address", "name": "new<PERSON><PERSON><PERSON><PERSON>_", "type": "address"}], "name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "outputs": [], "stateMutability": "nonpayable", "type": "function"}, {"inputs": [], "name": "symbol", "outputs": [{"internalType": "string", "name": "", "type": "string"}], "stateMutability": "view", "type": "function"}, {"inputs": [], "name": "totalSupply", "outputs": [{"internalType": "uint256", "name": "", "type": "uint256"}], "stateMutability": "view", "type": "function"}, {"inputs": [{"internalType": "address", "name": "to", "type": "address"}, {"internalType": "uint256", "name": "amount", "type": "uint256"}], "name": "transfer", "outputs": [{"internalType": "bool", "name": "", "type": "bool"}], "stateMutability": "nonpayable", "type": "function"}, {"inputs": [{"internalType": "address", "name": "from", "type": "address"}, {"internalType": "address", "name": "to", "type": "address"}, {"internalType": "uint256", "name": "amount", "type": "uint256"}], "name": "transferFrom", "outputs": [{"internalType": "bool", "name": "", "type": "bool"}], "stateMutability": "nonpayable", "type": "function"}, {"inputs": [{"internalType": "address", "name": "new<PERSON>wner", "type": "address"}], "name": "transferOwnership", "outputs": [], "stateMutability": "nonpayable", "type": "function"}], "bytecode": "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", "deployedBytecode": "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", "linkReferences": {}, "deployedLinkReferences": {}}