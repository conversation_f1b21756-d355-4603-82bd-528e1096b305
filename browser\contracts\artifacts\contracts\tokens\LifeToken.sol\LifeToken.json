{"_format": "hh-sol-artifact-1", "contractName": "LifeToken", "sourceName": "contracts/tokens/LifeToken.sol", "abi": [{"inputs": [{"internalType": "address", "name": "initialOwner_", "type": "address"}], "stateMutability": "nonpayable", "type": "constructor"}, {"anonymous": false, "inputs": [{"indexed": true, "internalType": "address", "name": "owner", "type": "address"}, {"indexed": true, "internalType": "address", "name": "spender", "type": "address"}, {"indexed": false, "internalType": "uint256", "name": "value", "type": "uint256"}], "name": "Approval", "type": "event"}, {"anonymous": false, "inputs": [{"indexed": true, "internalType": "address", "name": "account", "type": "address"}, {"indexed": false, "internalType": "uint256", "name": "oldAmount", "type": "uint256"}, {"indexed": false, "internalType": "uint256", "name": "newAmount", "type": "uint256"}, {"indexed": false, "internalType": "uint256", "name": "timestamp", "type": "uint256"}], "name": "BaseBalanceAdjusted", "type": "event"}, {"anonymous": false, "inputs": [{"indexed": true, "internalType": "address", "name": "newDistributor", "type": "address"}], "name": "DistributorUpdated", "type": "event"}, {"anonymous": false, "inputs": [{"indexed": true, "internalType": "uint256", "name": "epoch", "type": "uint256"}, {"indexed": false, "internalType": "uint256", "name": "oldFactor", "type": "uint256"}, {"indexed": false, "internalType": "uint256", "name": "newFactor", "type": "uint256"}, {"indexed": true, "internalType": "address", "name": "caller", "type": "address"}], "name": "EmergencyRebase", "type": "event"}, {"anonymous": false, "inputs": [{"indexed": true, "internalType": "address", "name": "account", "type": "address"}, {"indexed": false, "internalType": "bool", "name": "oldExcluded", "type": "bool"}, {"indexed": false, "internalType": "bool", "name": "newExcluded", "type": "bool"}], "name": "ExcludedFromRebase", "type": "event"}, {"anonymous": false, "inputs": [{"indexed": true, "internalType": "address", "name": "recipient", "type": "address"}, {"indexed": false, "internalType": "uint256", "name": "amount", "type": "uint256"}], "name": "Mint", "type": "event"}, {"anonymous": false, "inputs": [{"indexed": true, "internalType": "address", "name": "canceled<PERSON>wner", "type": "address"}], "name": "OwnershipTransferCanceled", "type": "event"}, {"anonymous": false, "inputs": [{"indexed": true, "internalType": "address", "name": "pending<PERSON><PERSON>er", "type": "address"}], "name": "OwnershipTransferStarted", "type": "event"}, {"anonymous": false, "inputs": [{"indexed": true, "internalType": "address", "name": "previousOwner", "type": "address"}, {"indexed": true, "internalType": "address", "name": "new<PERSON>wner", "type": "address"}], "name": "OwnershipTransferred", "type": "event"}, {"anonymous": false, "inputs": [{"indexed": true, "internalType": "uint256", "name": "epoch", "type": "uint256"}, {"indexed": false, "internalType": "uint256", "name": "oldFactor", "type": "uint256"}, {"indexed": false, "internalType": "uint256", "name": "newFactor", "type": "uint256"}], "name": "Rebase", "type": "event"}, {"anonymous": false, "inputs": [{"indexed": true, "internalType": "address", "name": "newRebaser", "type": "address"}], "name": "RebaserUpdated", "type": "event"}, {"anonymous": false, "inputs": [{"indexed": true, "internalType": "address", "name": "from", "type": "address"}, {"indexed": true, "internalType": "address", "name": "to", "type": "address"}, {"indexed": false, "internalType": "uint256", "name": "value", "type": "uint256"}], "name": "Transfer", "type": "event"}, {"inputs": [], "name": "INITIAL_SUPPLY", "outputs": [{"internalType": "uint256", "name": "", "type": "uint256"}], "stateMutability": "view", "type": "function"}, {"inputs": [], "name": "MAX_REBASE_FACTOR", "outputs": [{"internalType": "uint256", "name": "", "type": "uint256"}], "stateMutability": "view", "type": "function"}, {"inputs": [], "name": "MAX_SUPPLY", "outputs": [{"internalType": "uint256", "name": "", "type": "uint256"}], "stateMutability": "view", "type": "function"}, {"inputs": [], "name": "MIN_SUPPLY", "outputs": [{"internalType": "uint256", "name": "", "type": "uint256"}], "stateMutability": "view", "type": "function"}, {"inputs": [], "name": "REBASE_FACTOR_PRECISION", "outputs": [{"internalType": "uint256", "name": "", "type": "uint256"}], "stateMutability": "view", "type": "function"}, {"inputs": [], "name": "TOKEN_UNIT", "outputs": [{"internalType": "uint256", "name": "", "type": "uint256"}], "stateMutability": "view", "type": "function"}, {"inputs": [], "name": "acceptOwnership", "outputs": [], "stateMutability": "nonpayable", "type": "function"}, {"inputs": [{"internalType": "address", "name": "owner", "type": "address"}, {"internalType": "address", "name": "spender", "type": "address"}], "name": "allowance", "outputs": [{"internalType": "uint256", "name": "", "type": "uint256"}], "stateMutability": "view", "type": "function"}, {"inputs": [{"internalType": "address", "name": "spender", "type": "address"}, {"internalType": "uint256", "name": "amount", "type": "uint256"}], "name": "approve", "outputs": [{"internalType": "bool", "name": "", "type": "bool"}], "stateMutability": "nonpayable", "type": "function"}, {"inputs": [{"internalType": "address", "name": "account", "type": "address"}], "name": "balanceOf", "outputs": [{"internalType": "uint256", "name": "", "type": "uint256"}], "stateMutability": "view", "type": "function"}, {"inputs": [], "name": "cancelOwnershipTransfer", "outputs": [], "stateMutability": "nonpayable", "type": "function"}, {"inputs": [], "name": "decimals", "outputs": [{"internalType": "uint8", "name": "", "type": "uint8"}], "stateMutability": "view", "type": "function"}, {"inputs": [{"internalType": "address", "name": "spender", "type": "address"}, {"internalType": "uint256", "name": "subtractedValue", "type": "uint256"}], "name": "decreaseAllowance", "outputs": [{"internalType": "bool", "name": "", "type": "bool"}], "stateMutability": "nonpayable", "type": "function"}, {"inputs": [], "name": "distributor", "outputs": [{"internalType": "address", "name": "", "type": "address"}], "stateMutability": "view", "type": "function"}, {"inputs": [{"internalType": "uint256", "name": "newRebaseFactor", "type": "uint256"}], "name": "emergencyRebase", "outputs": [], "stateMutability": "nonpayable", "type": "function"}, {"inputs": [{"internalType": "address", "name": "account_", "type": "address"}], "name": "excludeFromRebase", "outputs": [], "stateMutability": "nonpayable", "type": "function"}, {"inputs": [{"internalType": "address", "name": "account", "type": "address"}], "name": "getBaseBalanceHistory", "outputs": [{"components": [{"internalType": "uint256", "name": "amount", "type": "uint256"}, {"internalType": "uint256", "name": "timestamp", "type": "uint256"}], "internalType": "struct LifeToken.BaseBalanceRecord[]", "name": "", "type": "tuple[]"}], "stateMutability": "view", "type": "function"}, {"inputs": [], "name": "getExcludedFromRebaseTotal", "outputs": [{"internalType": "uint256", "name": "", "type": "uint256"}], "stateMutability": "view", "type": "function"}, {"inputs": [{"internalType": "address", "name": "account", "type": "address"}], "name": "includeInRebase", "outputs": [], "stateMutability": "nonpayable", "type": "function"}, {"inputs": [{"internalType": "address", "name": "spender", "type": "address"}, {"internalType": "uint256", "name": "addedValue", "type": "uint256"}], "name": "increaseAllowance", "outputs": [{"internalType": "bool", "name": "", "type": "bool"}], "stateMutability": "nonpayable", "type": "function"}, {"inputs": [], "name": "initialDistributionDone", "outputs": [{"internalType": "bool", "name": "", "type": "bool"}], "stateMutability": "view", "type": "function"}, {"inputs": [{"internalType": "address", "name": "account", "type": "address"}], "name": "isExcludedFromRebase", "outputs": [{"internalType": "bool", "name": "", "type": "bool"}], "stateMutability": "view", "type": "function"}, {"inputs": [{"internalType": "address", "name": "recipient", "type": "address"}], "name": "mintRemainingSupply", "outputs": [], "stateMutability": "nonpayable", "type": "function"}, {"inputs": [], "name": "name", "outputs": [{"internalType": "string", "name": "", "type": "string"}], "stateMutability": "view", "type": "function"}, {"inputs": [], "name": "owner", "outputs": [{"internalType": "address", "name": "", "type": "address"}], "stateMutability": "view", "type": "function"}, {"inputs": [], "name": "ownershipTransferTime", "outputs": [{"internalType": "uint256", "name": "", "type": "uint256"}], "stateMutability": "view", "type": "function"}, {"inputs": [{"internalType": "uint256", "name": "newRebaseFactor", "type": "uint256"}], "name": "rebase", "outputs": [], "stateMutability": "nonpayable", "type": "function"}, {"inputs": [], "name": "rebaseConfig", "outputs": [{"internalType": "uint256", "name": "lastRebaseTimestamp", "type": "uint256"}, {"internalType": "uint256", "name": "minRebaseInterval", "type": "uint256"}, {"internalType": "uint256", "name": "rebaseFactor", "type": "uint256"}, {"internalType": "uint256", "name": "epoch", "type": "uint256"}], "stateMutability": "view", "type": "function"}, {"inputs": [], "name": "rebaseEpoch", "outputs": [{"internalType": "uint256", "name": "", "type": "uint256"}], "stateMutability": "view", "type": "function"}, {"inputs": [], "name": "rebaser", "outputs": [{"internalType": "address", "name": "", "type": "address"}], "stateMutability": "view", "type": "function"}, {"inputs": [], "name": "renounceOwnership", "outputs": [], "stateMutability": "view", "type": "function"}, {"inputs": [{"internalType": "address", "name": "newDistributor_", "type": "address"}], "name": "setDistributor", "outputs": [], "stateMutability": "nonpayable", "type": "function"}, {"inputs": [{"internalType": "address", "name": "new<PERSON><PERSON><PERSON><PERSON>_", "type": "address"}], "name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "outputs": [], "stateMutability": "nonpayable", "type": "function"}, {"inputs": [], "name": "symbol", "outputs": [{"internalType": "string", "name": "", "type": "string"}], "stateMutability": "view", "type": "function"}, {"inputs": [], "name": "totalSupply", "outputs": [{"internalType": "uint256", "name": "", "type": "uint256"}], "stateMutability": "view", "type": "function"}, {"inputs": [{"internalType": "address", "name": "to", "type": "address"}, {"internalType": "uint256", "name": "amount", "type": "uint256"}], "name": "transfer", "outputs": [{"internalType": "bool", "name": "", "type": "bool"}], "stateMutability": "nonpayable", "type": "function"}, {"inputs": [{"internalType": "address", "name": "from", "type": "address"}, {"internalType": "address", "name": "to", "type": "address"}, {"internalType": "uint256", "name": "amount", "type": "uint256"}], "name": "transferFrom", "outputs": [{"internalType": "bool", "name": "", "type": "bool"}], "stateMutability": "nonpayable", "type": "function"}, {"inputs": [{"internalType": "address", "name": "new<PERSON>wner", "type": "address"}], "name": "transferOwnership", "outputs": [], "stateMutability": "nonpayable", "type": "function"}], "bytecode": "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", "deployedBytecode": "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", "linkReferences": {}, "deployedLinkReferences": {}}