{"_format": "hh-sol-artifact-1", "contractName": "BaseRewards", "sourceName": "contracts/rewards/BaseRewards.sol", "abi": [{"inputs": [{"internalType": "address", "name": "_stakingToken", "type": "address"}, {"internalType": "address", "name": "_rewardsToken", "type": "address"}, {"internalType": "address", "name": "initialOwner", "type": "address"}], "stateMutability": "nonpayable", "type": "constructor"}, {"anonymous": false, "inputs": [{"indexed": false, "internalType": "uint256", "name": "newPeriod", "type": "uint256"}], "name": "MinStakingPeriodUpdated", "type": "event"}, {"anonymous": false, "inputs": [{"indexed": true, "internalType": "address", "name": "previousOwner", "type": "address"}, {"indexed": true, "internalType": "address", "name": "new<PERSON>wner", "type": "address"}], "name": "OwnershipTransferred", "type": "event"}, {"anonymous": false, "inputs": [{"indexed": true, "internalType": "address", "name": "user", "type": "address"}, {"indexed": false, "internalType": "uint256", "name": "amount", "type": "uint256"}], "name": "<PERSON>wardEx<PERSON>", "type": "event"}, {"anonymous": false, "inputs": [{"indexed": true, "internalType": "address", "name": "user", "type": "address"}, {"indexed": false, "internalType": "uint256", "name": "reward", "type": "uint256"}], "name": "RewardPaid", "type": "event"}, {"anonymous": false, "inputs": [{"indexed": false, "internalType": "uint256", "name": "oldRate", "type": "uint256"}, {"indexed": false, "internalType": "uint256", "name": "newRate", "type": "uint256"}, {"indexed": true, "internalType": "address", "name": "admin", "type": "address"}], "name": "RewardRateUpdated", "type": "event"}, {"anonymous": false, "inputs": [{"indexed": true, "internalType": "address", "name": "user", "type": "address"}, {"indexed": false, "internalType": "uint256", "name": "amount", "type": "uint256"}], "name": "Staked", "type": "event"}, {"anonymous": false, "inputs": [{"indexed": true, "internalType": "address", "name": "token", "type": "address"}, {"indexed": false, "internalType": "uint256", "name": "amount", "type": "uint256"}, {"indexed": true, "internalType": "address", "name": "admin", "type": "address"}], "name": "TokensRescued", "type": "event"}, {"anonymous": false, "inputs": [{"indexed": true, "internalType": "address", "name": "user", "type": "address"}, {"indexed": false, "internalType": "uint256", "name": "amount", "type": "uint256"}], "name": "Withdrawn", "type": "event"}, {"inputs": [], "name": "BASIS_POINTS", "outputs": [{"internalType": "uint256", "name": "", "type": "uint256"}], "stateMutability": "view", "type": "function"}, {"inputs": [], "name": "MAX_CLAIM_PERIOD", "outputs": [{"internalType": "uint256", "name": "", "type": "uint256"}], "stateMutability": "view", "type": "function"}, {"inputs": [], "name": "MAX_PERIODS_PROCESSED", "outputs": [{"internalType": "uint256", "name": "", "type": "uint256"}], "stateMutability": "view", "type": "function"}, {"inputs": [], "name": "MAX_REWARD_AMOUNT", "outputs": [{"internalType": "uint256", "name": "", "type": "uint256"}], "stateMutability": "view", "type": "function"}, {"inputs": [], "name": "MAX_REWARD_RATE", "outputs": [{"internalType": "uint256", "name": "", "type": "uint256"}], "stateMutability": "view", "type": "function"}, {"inputs": [], "name": "MAX_STAKING_DAYS", "outputs": [{"internalType": "uint256", "name": "", "type": "uint256"}], "stateMutability": "view", "type": "function"}, {"inputs": [], "name": "MIN_STAKING_PERIOD", "outputs": [{"internalType": "uint256", "name": "", "type": "uint256"}], "stateMutability": "view", "type": "function"}, {"inputs": [], "name": "RATE_CHANGE_COOLDOWN", "outputs": [{"internalType": "uint256", "name": "", "type": "uint256"}], "stateMutability": "view", "type": "function"}, {"inputs": [{"internalType": "uint256", "name": "stakingAmount", "type": "uint256"}, {"internalType": "uint256", "name": "baseRate", "type": "uint256"}, {"internalType": "uint256", "name": "timeMultiplier", "type": "uint256"}], "name": "_safeCalculateBaseReward", "outputs": [{"internalType": "uint256", "name": "result", "type": "uint256"}], "stateMutability": "pure", "type": "function"}, {"inputs": [], "name": "adminControl", "outputs": [{"internalType": "contract IAdminControl", "name": "", "type": "address"}], "stateMutability": "view", "type": "function"}, {"inputs": [{"internalType": "address", "name": "account", "type": "address"}], "name": "balanceOf", "outputs": [{"internalType": "uint256", "name": "", "type": "uint256"}], "stateMutability": "view", "type": "function"}, {"inputs": [{"internalType": "address", "name": "user", "type": "address"}, {"internalType": "uint256", "name": "stakingAmount", "type": "uint256"}, {"internalType": "uint256", "name": "stakingDuration", "type": "uint256"}], "name": "calculateRewards", "outputs": [{"internalType": "uint256", "name": "totalReward", "type": "uint256"}], "stateMutability": "view", "type": "function"}, {"inputs": [], "name": "claimReward", "outputs": [], "stateMutability": "nonpayable", "type": "function"}, {"inputs": [{"internalType": "address", "name": "account", "type": "address"}], "name": "clearExpiredRewards", "outputs": [], "stateMutability": "nonpayable", "type": "function"}, {"inputs": [{"internalType": "address", "name": "account", "type": "address"}], "name": "earned", "outputs": [{"internalType": "uint256", "name": "", "type": "uint256"}], "stateMutability": "view", "type": "function"}, {"inputs": [], "name": "lastRateChange", "outputs": [{"internalType": "uint256", "name": "", "type": "uint256"}], "stateMutability": "view", "type": "function"}, {"inputs": [], "name": "lastUpdateTime", "outputs": [{"internalType": "uint256", "name": "", "type": "uint256"}], "stateMutability": "view", "type": "function"}, {"inputs": [], "name": "minStakingPeriod", "outputs": [{"internalType": "uint256", "name": "", "type": "uint256"}], "stateMutability": "view", "type": "function"}, {"inputs": [], "name": "owner", "outputs": [{"internalType": "address", "name": "", "type": "address"}], "stateMutability": "view", "type": "function"}, {"inputs": [], "name": "pauseRate<PERSON>hanges", "outputs": [], "stateMutability": "nonpayable", "type": "function"}, {"inputs": [], "name": "rateChangePaused", "outputs": [{"internalType": "bool", "name": "", "type": "bool"}], "stateMutability": "view", "type": "function"}, {"inputs": [], "name": "renounceOwnership", "outputs": [], "stateMutability": "nonpayable", "type": "function"}, {"inputs": [{"internalType": "address", "name": "token", "type": "address"}, {"internalType": "uint256", "name": "amount", "type": "uint256"}], "name": "rescueTokens", "outputs": [], "stateMutability": "nonpayable", "type": "function"}, {"inputs": [], "name": "rewardConfig", "outputs": [{"internalType": "uint256", "name": "baseRewardRate", "type": "uint256"}, {"internalType": "uint256", "name": "communityBonusRate", "type": "uint256"}, {"internalType": "uint256", "name": "leaseBonusRate", "type": "uint256"}], "stateMutability": "view", "type": "function"}, {"inputs": [{"internalType": "address", "name": "", "type": "address"}], "name": "rewardExpirationTimestamps", "outputs": [{"internalType": "uint256", "name": "", "type": "uint256"}], "stateMutability": "view", "type": "function"}, {"inputs": [], "name": "rewardPerToken", "outputs": [{"internalType": "uint256", "name": "", "type": "uint256"}], "stateMutability": "view", "type": "function"}, {"inputs": [], "name": "rewardPerTokenStored", "outputs": [{"internalType": "uint256", "name": "", "type": "uint256"}], "stateMutability": "view", "type": "function"}, {"inputs": [{"internalType": "uint256", "name": "", "type": "uint256"}], "name": "rewardPeriods", "outputs": [{"internalType": "uint256", "name": "rate", "type": "uint256"}, {"internalType": "uint256", "name": "startTime", "type": "uint256"}, {"internalType": "uint256", "name": "endTime", "type": "uint256"}], "stateMutability": "view", "type": "function"}, {"inputs": [{"internalType": "address", "name": "", "type": "address"}], "name": "rewards", "outputs": [{"internalType": "uint256", "name": "", "type": "uint256"}], "stateMutability": "view", "type": "function"}, {"inputs": [], "name": "rewardsToken", "outputs": [{"internalType": "contract ERC20", "name": "", "type": "address"}], "stateMutability": "view", "type": "function"}, {"inputs": [], "name": "rewardsTokenUnit", "outputs": [{"internalType": "uint256", "name": "", "type": "uint256"}], "stateMutability": "view", "type": "function"}, {"inputs": [{"internalType": "uint256", "name": "period", "type": "uint256"}], "name": "setMinStakingPeriod", "outputs": [], "stateMutability": "nonpayable", "type": "function"}, {"inputs": [{"internalType": "uint256", "name": "_rewardRate", "type": "uint256"}], "name": "setRewardRate", "outputs": [], "stateMutability": "nonpayable", "type": "function"}, {"inputs": [{"internalType": "uint256", "name": "amount", "type": "uint256"}], "name": "stake", "outputs": [], "stateMutability": "nonpayable", "type": "function"}, {"inputs": [], "name": "stakingToken", "outputs": [{"internalType": "contract ERC20", "name": "", "type": "address"}], "stateMutability": "view", "type": "function"}, {"inputs": [], "name": "stakingTokenUnit", "outputs": [{"internalType": "uint256", "name": "", "type": "uint256"}], "stateMutability": "view", "type": "function"}, {"inputs": [], "name": "totalSupply", "outputs": [{"internalType": "uint256", "name": "", "type": "uint256"}], "stateMutability": "view", "type": "function"}, {"inputs": [{"internalType": "address", "name": "new<PERSON>wner", "type": "address"}], "name": "transferOwnership", "outputs": [], "stateMutability": "nonpayable", "type": "function"}, {"inputs": [], "name": "unpauseRateChanges", "outputs": [], "stateMutability": "nonpayable", "type": "function"}, {"inputs": [{"internalType": "address", "name": "", "type": "address"}], "name": "userRewardPerTokenPaid", "outputs": [{"internalType": "uint256", "name": "", "type": "uint256"}], "stateMutability": "view", "type": "function"}, {"inputs": [{"internalType": "uint256", "name": "amount", "type": "uint256"}], "name": "withdraw", "outputs": [], "stateMutability": "nonpayable", "type": "function"}], "bytecode": "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", "deployedBytecode": "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", "linkReferences": {}, "deployedLinkReferences": {}}