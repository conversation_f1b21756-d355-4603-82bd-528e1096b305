{"_format": "hh-sol-artifact-1", "contractName": "BaseRewards", "sourceName": "contracts/rewards/BaseRewards.sol", "abi": [{"inputs": [{"internalType": "address", "name": "_stakingToken", "type": "address"}, {"internalType": "address", "name": "_rewardsToken", "type": "address"}, {"internalType": "address", "name": "initialOwner", "type": "address"}], "stateMutability": "nonpayable", "type": "constructor"}, {"anonymous": false, "inputs": [{"indexed": false, "internalType": "uint256", "name": "newPeriod", "type": "uint256"}], "name": "MinStakingPeriodUpdated", "type": "event"}, {"anonymous": false, "inputs": [{"indexed": true, "internalType": "address", "name": "previousOwner", "type": "address"}, {"indexed": true, "internalType": "address", "name": "new<PERSON>wner", "type": "address"}], "name": "OwnershipTransferred", "type": "event"}, {"anonymous": false, "inputs": [{"indexed": true, "internalType": "address", "name": "user", "type": "address"}, {"indexed": false, "internalType": "uint256", "name": "amount", "type": "uint256"}], "name": "<PERSON>wardEx<PERSON>", "type": "event"}, {"anonymous": false, "inputs": [{"indexed": true, "internalType": "address", "name": "user", "type": "address"}, {"indexed": false, "internalType": "uint256", "name": "reward", "type": "uint256"}], "name": "RewardPaid", "type": "event"}, {"anonymous": false, "inputs": [{"indexed": false, "internalType": "uint256", "name": "oldRate", "type": "uint256"}, {"indexed": false, "internalType": "uint256", "name": "newRate", "type": "uint256"}, {"indexed": true, "internalType": "address", "name": "admin", "type": "address"}], "name": "RewardRateUpdated", "type": "event"}, {"anonymous": false, "inputs": [{"indexed": true, "internalType": "address", "name": "user", "type": "address"}, {"indexed": false, "internalType": "uint256", "name": "amount", "type": "uint256"}], "name": "Staked", "type": "event"}, {"anonymous": false, "inputs": [{"indexed": true, "internalType": "address", "name": "token", "type": "address"}, {"indexed": false, "internalType": "uint256", "name": "amount", "type": "uint256"}, {"indexed": true, "internalType": "address", "name": "admin", "type": "address"}], "name": "TokensRescued", "type": "event"}, {"anonymous": false, "inputs": [{"indexed": true, "internalType": "address", "name": "user", "type": "address"}, {"indexed": false, "internalType": "uint256", "name": "amount", "type": "uint256"}], "name": "Withdrawn", "type": "event"}, {"inputs": [], "name": "BASIS_POINTS", "outputs": [{"internalType": "uint256", "name": "", "type": "uint256"}], "stateMutability": "view", "type": "function"}, {"inputs": [], "name": "MAX_CLAIM_PERIOD", "outputs": [{"internalType": "uint256", "name": "", "type": "uint256"}], "stateMutability": "view", "type": "function"}, {"inputs": [], "name": "MAX_PERIODS_PROCESSED", "outputs": [{"internalType": "uint256", "name": "", "type": "uint256"}], "stateMutability": "view", "type": "function"}, {"inputs": [], "name": "MAX_REWARD_AMOUNT", "outputs": [{"internalType": "uint256", "name": "", "type": "uint256"}], "stateMutability": "view", "type": "function"}, {"inputs": [], "name": "MAX_REWARD_RATE", "outputs": [{"internalType": "uint256", "name": "", "type": "uint256"}], "stateMutability": "view", "type": "function"}, {"inputs": [], "name": "MAX_STAKING_DAYS", "outputs": [{"internalType": "uint256", "name": "", "type": "uint256"}], "stateMutability": "view", "type": "function"}, {"inputs": [], "name": "MIN_STAKING_PERIOD", "outputs": [{"internalType": "uint256", "name": "", "type": "uint256"}], "stateMutability": "view", "type": "function"}, {"inputs": [], "name": "RATE_CHANGE_COOLDOWN", "outputs": [{"internalType": "uint256", "name": "", "type": "uint256"}], "stateMutability": "view", "type": "function"}, {"inputs": [{"internalType": "uint256", "name": "stakingAmount", "type": "uint256"}, {"internalType": "uint256", "name": "baseRate", "type": "uint256"}, {"internalType": "uint256", "name": "timeMultiplier", "type": "uint256"}], "name": "_safeCalculateBaseReward", "outputs": [{"internalType": "uint256", "name": "result", "type": "uint256"}], "stateMutability": "pure", "type": "function"}, {"inputs": [], "name": "adminControl", "outputs": [{"internalType": "contract IAdminControl", "name": "", "type": "address"}], "stateMutability": "view", "type": "function"}, {"inputs": [{"internalType": "address", "name": "account", "type": "address"}], "name": "balanceOf", "outputs": [{"internalType": "uint256", "name": "", "type": "uint256"}], "stateMutability": "view", "type": "function"}, {"inputs": [{"internalType": "address", "name": "user", "type": "address"}, {"internalType": "uint256", "name": "stakingAmount", "type": "uint256"}, {"internalType": "uint256", "name": "stakingDuration", "type": "uint256"}], "name": "calculateRewards", "outputs": [{"internalType": "uint256", "name": "totalReward", "type": "uint256"}], "stateMutability": "view", "type": "function"}, {"inputs": [], "name": "claimReward", "outputs": [], "stateMutability": "nonpayable", "type": "function"}, {"inputs": [{"internalType": "address", "name": "account", "type": "address"}], "name": "clearExpiredRewards", "outputs": [], "stateMutability": "nonpayable", "type": "function"}, {"inputs": [{"internalType": "address", "name": "account", "type": "address"}], "name": "earned", "outputs": [{"internalType": "uint256", "name": "", "type": "uint256"}], "stateMutability": "view", "type": "function"}, {"inputs": [], "name": "lastRateChange", "outputs": [{"internalType": "uint256", "name": "", "type": "uint256"}], "stateMutability": "view", "type": "function"}, {"inputs": [], "name": "lastUpdateTime", "outputs": [{"internalType": "uint256", "name": "", "type": "uint256"}], "stateMutability": "view", "type": "function"}, {"inputs": [], "name": "minStakingPeriod", "outputs": [{"internalType": "uint256", "name": "", "type": "uint256"}], "stateMutability": "view", "type": "function"}, {"inputs": [], "name": "owner", "outputs": [{"internalType": "address", "name": "", "type": "address"}], "stateMutability": "view", "type": "function"}, {"inputs": [], "name": "pauseRate<PERSON>hanges", "outputs": [], "stateMutability": "nonpayable", "type": "function"}, {"inputs": [], "name": "rateChangePaused", "outputs": [{"internalType": "bool", "name": "", "type": "bool"}], "stateMutability": "view", "type": "function"}, {"inputs": [], "name": "renounceOwnership", "outputs": [], "stateMutability": "nonpayable", "type": "function"}, {"inputs": [{"internalType": "address", "name": "token", "type": "address"}, {"internalType": "uint256", "name": "amount", "type": "uint256"}], "name": "rescueTokens", "outputs": [], "stateMutability": "nonpayable", "type": "function"}, {"inputs": [], "name": "rewardConfig", "outputs": [{"internalType": "uint256", "name": "baseRewardRate", "type": "uint256"}, {"internalType": "uint256", "name": "communityBonusRate", "type": "uint256"}, {"internalType": "uint256", "name": "leaseBonusRate", "type": "uint256"}], "stateMutability": "view", "type": "function"}, {"inputs": [{"internalType": "address", "name": "", "type": "address"}], "name": "rewardExpirationTimestamps", "outputs": [{"internalType": "uint256", "name": "", "type": "uint256"}], "stateMutability": "view", "type": "function"}, {"inputs": [], "name": "rewardPerToken", "outputs": [{"internalType": "uint256", "name": "", "type": "uint256"}], "stateMutability": "view", "type": "function"}, {"inputs": [], "name": "rewardPerTokenStored", "outputs": [{"internalType": "uint256", "name": "", "type": "uint256"}], "stateMutability": "view", "type": "function"}, {"inputs": [{"internalType": "uint256", "name": "", "type": "uint256"}], "name": "rewardPeriods", "outputs": [{"internalType": "uint256", "name": "rate", "type": "uint256"}, {"internalType": "uint256", "name": "startTime", "type": "uint256"}, {"internalType": "uint256", "name": "endTime", "type": "uint256"}], "stateMutability": "view", "type": "function"}, {"inputs": [{"internalType": "address", "name": "", "type": "address"}], "name": "rewards", "outputs": [{"internalType": "uint256", "name": "", "type": "uint256"}], "stateMutability": "view", "type": "function"}, {"inputs": [], "name": "rewardsToken", "outputs": [{"internalType": "contract ERC20", "name": "", "type": "address"}], "stateMutability": "view", "type": "function"}, {"inputs": [], "name": "rewardsTokenUnit", "outputs": [{"internalType": "uint256", "name": "", "type": "uint256"}], "stateMutability": "view", "type": "function"}, {"inputs": [{"internalType": "uint256", "name": "period", "type": "uint256"}], "name": "setMinStakingPeriod", "outputs": [], "stateMutability": "nonpayable", "type": "function"}, {"inputs": [{"internalType": "uint256", "name": "_rewardRate", "type": "uint256"}], "name": "setRewardRate", "outputs": [], "stateMutability": "nonpayable", "type": "function"}, {"inputs": [{"internalType": "uint256", "name": "amount", "type": "uint256"}], "name": "stake", "outputs": [], "stateMutability": "nonpayable", "type": "function"}, {"inputs": [], "name": "stakingToken", "outputs": [{"internalType": "contract ERC20", "name": "", "type": "address"}], "stateMutability": "view", "type": "function"}, {"inputs": [], "name": "stakingTokenUnit", "outputs": [{"internalType": "uint256", "name": "", "type": "uint256"}], "stateMutability": "view", "type": "function"}, {"inputs": [], "name": "totalSupply", "outputs": [{"internalType": "uint256", "name": "", "type": "uint256"}], "stateMutability": "view", "type": "function"}, {"inputs": [{"internalType": "address", "name": "new<PERSON>wner", "type": "address"}], "name": "transferOwnership", "outputs": [], "stateMutability": "nonpayable", "type": "function"}, {"inputs": [], "name": "unpauseRateChanges", "outputs": [], "stateMutability": "nonpayable", "type": "function"}, {"inputs": [{"internalType": "address", "name": "", "type": "address"}], "name": "userRewardPerTokenPaid", "outputs": [{"internalType": "uint256", "name": "", "type": "uint256"}], "stateMutability": "view", "type": "function"}, {"inputs": [{"internalType": "uint256", "name": "amount", "type": "uint256"}], "name": "withdraw", "outputs": [], "stateMutability": "nonpayable", "type": "function"}], "bytecode": "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", "deployedBytecode": "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", "linkReferences": {}, "deployedLinkReferences": {}}