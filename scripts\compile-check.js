const { ethers } = require("hardhat");

async function main() {
    console.log("🔧 Checking contract compilation...\n");

    try {
        // Check PropertyMarket compilation
        console.log("📦 Compiling PropertyMarket...");
        const PropertyMarket = await ethers.getContractFactory("PropertyMarket");
        console.log("✅ PropertyMarket compiled successfully");
        
        // Check MockERC721 compilation
        console.log("📦 Compiling MockERC721...");
        const MockERC721 = await ethers.getContractFactory("MockERC721");
        console.log("✅ MockERC721 compiled successfully");
        
        // Get contract bytecode sizes
        const propertyMarketBytecode = PropertyMarket.bytecode;
        const mockERC721Bytecode = MockERC721.bytecode;
        
        const propertyMarketSize = (propertyMarketBytecode.length - 2) / 2; // Remove 0x and divide by 2
        const mockERC721Size = (mockERC721Bytecode.length - 2) / 2;
        
        console.log("\n📊 Contract Sizes:");
        console.log("PropertyMarket:", propertyMarketSize, "bytes");
        console.log("MockERC721:", mockERC721Size, "bytes");
        
        // Check if PropertyMarket is within deployment limit
        const deploymentLimit = 24576; // 24KB
        console.log("\n🚦 Deployment Check:");
        console.log("PropertyMarket size:", propertyMarketSize, "bytes");
        console.log("Deployment limit:", deploymentLimit, "bytes");
        console.log("Within limit:", propertyMarketSize <= deploymentLimit ? "✅ YES" : "❌ NO");
        
        if (propertyMarketSize > deploymentLimit) {
            console.log("⚠️  WARNING: PropertyMarket exceeds deployment size limit!");
            console.log("   Consider optimizing the contract or splitting functionality.");
        }
        
        console.log("\n🎉 All contracts compiled successfully!");
        
    } catch (error) {
        console.error("❌ Compilation failed:");
        console.error(error.message);
        process.exit(1);
    }
}

if (require.main === module) {
    main()
        .then(() => process.exit(0))
        .catch((error) => {
            console.error(error);
            process.exit(1);
        });
}

module.exports = main;
