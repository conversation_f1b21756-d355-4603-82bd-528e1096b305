{"compiler": {"version": "0.8.24+commit.e11b9ed9"}, "language": "Solidity", "output": {"abi": [{"inputs": [{"internalType": "address", "name": "_adminControl", "type": "address"}, {"internalType": "address", "name": "_nftiAddress", "type": "address"}, {"internalType": "address", "name": "_nftmAddress", "type": "address"}], "stateMutability": "nonpayable", "type": "constructor"}, {"anonymous": false, "inputs": [{"indexed": true, "internalType": "uint256", "name": "tokenId", "type": "uint256"}, {"indexed": false, "internalType": "uint256", "name": "newPrice", "type": "uint256"}, {"indexed": false, "internalType": "address", "name": "newPaymentToken", "type": "address"}], "name": "ListingUpdated", "type": "event"}, {"anonymous": false, "inputs": [{"indexed": true, "internalType": "uint256", "name": "tokenId", "type": "uint256"}, {"indexed": true, "internalType": "address", "name": "seller", "type": "address"}, {"indexed": false, "internalType": "uint256", "name": "price", "type": "uint256"}, {"indexed": false, "internalType": "address", "name": "paymentToken", "type": "address"}], "name": "NewListing", "type": "event"}, {"anonymous": false, "inputs": [{"indexed": true, "internalType": "uint256", "name": "tokenId", "type": "uint256"}, {"indexed": false, "internalType": "address", "name": "buyer", "type": "address"}, {"indexed": false, "internalType": "uint256", "name": "price", "type": "uint256"}, {"indexed": false, "internalType": "address", "name": "paymentToken", "type": "address"}], "name": "PropertySold", "type": "event"}, {"inputs": [], "name": "adminControl", "outputs": [{"internalType": "contract AdminControl", "name": "", "type": "address"}], "stateMutability": "view", "type": "function"}, {"inputs": [{"internalType": "uint256", "name": "tokenId", "type": "uint256"}], "name": "getListingDetails", "outputs": [{"internalType": "address", "name": "seller", "type": "address"}, {"internalType": "uint256", "name": "price", "type": "uint256"}, {"internalType": "address", "name": "paymentToken", "type": "address"}, {"internalType": "enum PropertyMarket.PropertyStatus", "name": "status", "type": "uint8"}, {"internalType": "uint256", "name": "listTimestamp", "type": "uint256"}], "stateMutability": "view", "type": "function"}, {"inputs": [{"internalType": "address", "name": "", "type": "address"}, {"internalType": "uint256", "name": "", "type": "uint256"}], "name": "leaseTerms", "outputs": [{"internalType": "uint256", "name": "", "type": "uint256"}], "stateMutability": "view", "type": "function"}, {"inputs": [{"internalType": "uint256", "name": "tokenId", "type": "uint256"}, {"internalType": "uint256", "name": "price", "type": "uint256"}, {"internalType": "address", "name": "paymentToken", "type": "address"}], "name": "listProperty", "outputs": [], "stateMutability": "nonpayable", "type": "function"}, {"inputs": [{"internalType": "uint256", "name": "", "type": "uint256"}], "name": "listings", "outputs": [{"internalType": "uint256", "name": "tokenId", "type": "uint256"}, {"internalType": "address", "name": "seller", "type": "address"}, {"internalType": "uint256", "name": "price", "type": "uint256"}, {"internalType": "address", "name": "paymentToken", "type": "address"}, {"internalType": "enum PropertyMarket.PropertyStatus", "name": "status", "type": "uint8"}, {"internalType": "uint256", "name": "listTimestamp", "type": "uint256"}, {"internalType": "uint256", "name": "lastRenewed", "type": "uint256"}], "stateMutability": "view", "type": "function"}, {"inputs": [], "name": "nftiContract", "outputs": [{"internalType": "contract IERC721", "name": "", "type": "address"}], "stateMutability": "view", "type": "function"}, {"inputs": [], "name": "nftmContract", "outputs": [{"internalType": "contract IERC721", "name": "", "type": "address"}], "stateMutability": "view", "type": "function"}, {"inputs": [{"internalType": "uint256", "name": "tokenId", "type": "uint256"}, {"internalType": "uint256", "name": "offerPrice", "type": "uint256"}], "name": "purchaseProperty", "outputs": [], "stateMutability": "payable", "type": "function"}, {"inputs": [{"internalType": "uint256", "name": "tokenId", "type": "uint256"}, {"internalType": "uint256", "name": "newPrice", "type": "uint256"}, {"internalType": "address", "name": "newPaymentToken", "type": "address"}], "name": "updateListing", "outputs": [], "stateMutability": "nonpayable", "type": "function"}], "devdoc": {"kind": "dev", "methods": {}, "version": 1}, "userdoc": {"kind": "user", "methods": {}, "version": 1}}, "settings": {"compilationTarget": {"contracts/market/PropertyMarket.sol": "PropertyMarket"}, "evmVersion": "shanghai", "libraries": {}, "metadata": {"bytecodeHash": "ipfs"}, "optimizer": {"enabled": false, "runs": 200}, "remappings": []}, "sources": {"@openzeppelin/contracts/access/AccessControl.sol": {"keccak256": "0xa0e92d42942f4f57c5be50568dac11e9d00c93efcb458026e18d2d9b9b2e7308", "license": "MIT", "urls": ["bzz-raw://46326c0bb1e296b67185e81c918e0b40501b8b6386165855df0a3f3c634b6a80", "dweb:/ipfs/QmTwyrDYtsxsk6pymJTK94PnEpzsmkpUxFuzEiakDopy4Z"]}, "@openzeppelin/contracts/access/IAccessControl.sol": {"keccak256": "0xc1c2a7f1563b77050dc6d507db9f4ada5d042c1f6a9ddbffdc49c77cdc0a1606", "license": "MIT", "urls": ["bzz-raw://fd54abb96a6156d9a761f6fdad1d3004bc48d2d4fce47f40a3f91a7ae83fc3a1", "dweb:/ipfs/QmUrFSGkTDJ7WaZ6qPVVe3Gn5uN2viPb7x7QQ35UX4DofX"]}, "@openzeppelin/contracts/security/Pausable.sol": {"keccak256": "0x0849d93b16c9940beb286a7864ed02724b248b93e0d80ef6355af5ef15c64773", "license": "MIT", "urls": ["bzz-raw://4ddabb16009cd17eaca3143feadf450ac13e72919ebe2ca50e00f61cb78bc004", "dweb:/ipfs/QmSPwPxX7d6TTWakN5jy5wsaGkS1y9TW8fuhGSraMkLk2B"]}, "@openzeppelin/contracts/security/ReentrancyGuard.sol": {"keccak256": "0xa535a5df777d44e945dd24aa43a11e44b024140fc340ad0dfe42acf4002aade1", "license": "MIT", "urls": ["bzz-raw://41319e7f621f2dc3733511332c4fd032f8e32ad2aa7fd6f665c19741d9941a34", "dweb:/ipfs/QmcYR3bd862GD1Bc7jwrU9bGxrhUu5na1oP964bDCu2id1"]}, "@openzeppelin/contracts/token/ERC20/IERC20.sol": {"keccak256": "0xe06a3f08a987af6ad2e1c1e774405d4fe08f1694b67517438b467cecf0da0ef7", "license": "MIT", "urls": ["bzz-raw://df6f0c459663c9858b6cba2cda1d14a7d05a985bed6d2de72bd8e78c25ee79db", "dweb:/ipfs/QmeTTxZ7qVk9rjEv2R4CpCwdf8UMCcRqDNMvzNxHc3Fnn9"]}, "@openzeppelin/contracts/token/ERC721/IERC721.sol": {"keccak256": "0x5dc63d1c6a12fe1b17793e1745877b2fcbe1964c3edfd0a482fac21ca8f18261", "license": "MIT", "urls": ["bzz-raw://6b7f97c5960a50fd1822cb298551ffc908e37b7893a68d6d08bce18a11cb0f11", "dweb:/ipfs/QmQQvxBytoY1eBt3pRQDmvH2hZ2yjhs12YqVfzGm7KSURq"]}, "@openzeppelin/contracts/utils/Context.sol": {"keccak256": "0x493033a8d1b176a037b2cc6a04dad01a5c157722049bbecf632ca876224dd4b2", "license": "MIT", "urls": ["bzz-raw://6a708e8a5bdb1011c2c381c9a5cfd8a9a956d7d0a9dc1bd8bcdaf52f76ef2f12", "dweb:/ipfs/Qmax9WHBnVsZP46ZxEMNRQpLQnrdE4dK8LehML1Py8FowF"]}, "@openzeppelin/contracts/utils/introspection/ERC165.sol": {"keccak256": "0xddce8e17e3d3f9ed818b4f4c4478a8262aab8b11ed322f1bf5ed705bb4bd97fa", "license": "MIT", "urls": ["bzz-raw://8084aa71a4cc7d2980972412a88fe4f114869faea3fefa5436431644eb5c0287", "dweb:/ipfs/Qmbqfs5dRdPvHVKY8kTaeyc65NdqXRQwRK7h9s5UJEhD1p"]}, "@openzeppelin/contracts/utils/introspection/IERC165.sol": {"keccak256": "0x79796192ec90263f21b464d5bc90b777a525971d3de8232be80d9c4f9fb353b8", "license": "MIT", "urls": ["bzz-raw://f6fda447a62815e8064f47eff0dd1cf58d9207ad69b5d32280f8d7ed1d1e4621", "dweb:/ipfs/QmfDRc7pxfaXB2Dh9np5Uf29Na3pQ7tafRS684wd3GLjVL"]}, "@openzeppelin/contracts/utils/structs/EnumerableSet.sol": {"keccak256": "0x9b161e97b8967d4bc0c08d25741889a30692cdda4b71910bf1b0e315f1962212", "license": "MIT", "urls": ["bzz-raw://821500c39d095eaa25b06f46e65f1d0e97c4c96b39227f79077dee7d9e84051f", "dweb:/ipfs/QmSTeGorKzSWF6HAmxo32uCZiEuKNvreQdUiX1AaYtUMTz"]}, "contracts/governance/AdminControl.sol": {"keccak256": "0x3528ca41c6097f19bbfd62e3f33e6df1cc685045824febe7dcae2724ee0b8a87", "license": "MIT", "urls": ["bzz-raw://5f68988266cecbab1ced4b09d80171bb015b999392aec1bc6354b4f7ebd80a67", "dweb:/ipfs/QmRxwAnXXZmdicAD5fh4r58ngCVSnvwbz82dFWKibCYnm7"]}, "contracts/market/PropertyMarket.sol": {"keccak256": "0x45fb8db173dee3e8275e1112185163c57058ca9cd99b20a50f6a33807f76b7c9", "license": "MIT", "urls": ["bzz-raw://c0cf5d9f82f30e6ec7320a2a504542bcfba22fa5e6d5bec86ee160deb0ad8def", "dweb:/ipfs/QmawzXQxrKdoob5cXmA8a1hQgRjx4tYYEhCbs1yzhvtq7m"]}}, "version": 1}