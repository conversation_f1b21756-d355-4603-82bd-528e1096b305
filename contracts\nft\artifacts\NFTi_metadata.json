{"compiler": {"version": "0.8.24+commit.e11b9ed9"}, "language": "Solidity", "output": {"abi": [{"inputs": [], "stateMutability": "nonpayable", "type": "constructor"}, {"inputs": [{"internalType": "address", "name": "sender", "type": "address"}, {"internalType": "uint256", "name": "tokenId", "type": "uint256"}, {"internalType": "address", "name": "owner", "type": "address"}], "name": "ERC721IncorrectOwner", "type": "error"}, {"inputs": [{"internalType": "address", "name": "operator", "type": "address"}, {"internalType": "uint256", "name": "tokenId", "type": "uint256"}], "name": "ERC721InsufficientApproval", "type": "error"}, {"inputs": [{"internalType": "address", "name": "approver", "type": "address"}], "name": "ERC721InvalidApprover", "type": "error"}, {"inputs": [{"internalType": "address", "name": "operator", "type": "address"}], "name": "ERC721InvalidOperator", "type": "error"}, {"inputs": [{"internalType": "address", "name": "owner", "type": "address"}], "name": "ERC721InvalidOwner", "type": "error"}, {"inputs": [{"internalType": "address", "name": "receiver", "type": "address"}], "name": "ERC721InvalidReceiver", "type": "error"}, {"inputs": [{"internalType": "address", "name": "sender", "type": "address"}], "name": "ERC721InvalidSender", "type": "error"}, {"inputs": [{"internalType": "uint256", "name": "tokenId", "type": "uint256"}], "name": "ERC721NonexistentToken", "type": "error"}, {"anonymous": false, "inputs": [{"indexed": true, "internalType": "address", "name": "owner", "type": "address"}, {"indexed": true, "internalType": "address", "name": "approved", "type": "address"}, {"indexed": true, "internalType": "uint256", "name": "tokenId", "type": "uint256"}], "name": "Approval", "type": "event"}, {"anonymous": false, "inputs": [{"indexed": true, "internalType": "address", "name": "owner", "type": "address"}, {"indexed": true, "internalType": "address", "name": "operator", "type": "address"}, {"indexed": false, "internalType": "bool", "name": "approved", "type": "bool"}], "name": "ApprovalForAll", "type": "event"}, {"anonymous": false, "inputs": [{"indexed": true, "internalType": "address", "name": "from", "type": "address"}, {"indexed": true, "internalType": "address", "name": "to", "type": "address"}, {"indexed": true, "internalType": "uint256", "name": "tokenId", "type": "uint256"}], "name": "Transfer", "type": "event"}, {"inputs": [{"internalType": "address", "name": "to", "type": "address"}, {"internalType": "uint256", "name": "tokenId", "type": "uint256"}], "name": "approve", "outputs": [], "stateMutability": "nonpayable", "type": "function"}, {"inputs": [{"internalType": "address", "name": "owner", "type": "address"}], "name": "balanceOf", "outputs": [{"internalType": "uint256", "name": "", "type": "uint256"}], "stateMutability": "view", "type": "function"}, {"inputs": [{"internalType": "uint256", "name": "tokenId", "type": "uint256"}], "name": "burn", "outputs": [], "stateMutability": "nonpayable", "type": "function"}, {"inputs": [{"internalType": "uint256", "name": "tokenId", "type": "uint256"}], "name": "getApproved", "outputs": [{"internalType": "address", "name": "", "type": "address"}], "stateMutability": "view", "type": "function"}, {"inputs": [{"internalType": "address", "name": "owner", "type": "address"}, {"internalType": "address", "name": "operator", "type": "address"}], "name": "isApprovedForAll", "outputs": [{"internalType": "bool", "name": "", "type": "bool"}], "stateMutability": "view", "type": "function"}, {"inputs": [], "name": "marketContract", "outputs": [{"internalType": "address", "name": "", "type": "address"}], "stateMutability": "view", "type": "function"}, {"inputs": [{"internalType": "address", "name": "to", "type": "address"}, {"internalType": "string", "name": "legalId", "type": "string"}, {"internalType": "bool", "name": "managed", "type": "bool"}], "name": "mint", "outputs": [{"internalType": "uint256", "name": "", "type": "uint256"}], "stateMutability": "nonpayable", "type": "function"}, {"inputs": [], "name": "name", "outputs": [{"internalType": "string", "name": "", "type": "string"}], "stateMutability": "view", "type": "function"}, {"inputs": [{"internalType": "uint256", "name": "tokenId", "type": "uint256"}], "name": "ownerOf", "outputs": [{"internalType": "address", "name": "", "type": "address"}], "stateMutability": "view", "type": "function"}, {"inputs": [{"internalType": "uint256", "name": "", "type": "uint256"}], "name": "properties", "outputs": [{"internalType": "string", "name": "legalId", "type": "string"}, {"internalType": "bool", "name": "isManaged", "type": "bool"}, {"internalType": "uint256", "name": "createdAt", "type": "uint256"}], "stateMutability": "view", "type": "function"}, {"inputs": [{"internalType": "address", "name": "from", "type": "address"}, {"internalType": "address", "name": "to", "type": "address"}, {"internalType": "uint256", "name": "tokenId", "type": "uint256"}], "name": "safeTransferFrom", "outputs": [], "stateMutability": "nonpayable", "type": "function"}, {"inputs": [{"internalType": "address", "name": "from", "type": "address"}, {"internalType": "address", "name": "to", "type": "address"}, {"internalType": "uint256", "name": "tokenId", "type": "uint256"}, {"internalType": "bytes", "name": "data", "type": "bytes"}], "name": "safeTransferFrom", "outputs": [], "stateMutability": "nonpayable", "type": "function"}, {"inputs": [{"internalType": "address", "name": "operator", "type": "address"}, {"internalType": "bool", "name": "approved", "type": "bool"}], "name": "setApprovalForAll", "outputs": [], "stateMutability": "nonpayable", "type": "function"}, {"inputs": [{"internalType": "bytes4", "name": "interfaceId", "type": "bytes4"}], "name": "supportsInterface", "outputs": [{"internalType": "bool", "name": "", "type": "bool"}], "stateMutability": "view", "type": "function"}, {"inputs": [], "name": "symbol", "outputs": [{"internalType": "string", "name": "", "type": "string"}], "stateMutability": "view", "type": "function"}, {"inputs": [], "name": "tokenCounter", "outputs": [{"internalType": "uint256", "name": "", "type": "uint256"}], "stateMutability": "view", "type": "function"}, {"inputs": [{"internalType": "uint256", "name": "tokenId", "type": "uint256"}], "name": "tokenURI", "outputs": [{"internalType": "string", "name": "", "type": "string"}], "stateMutability": "view", "type": "function"}, {"inputs": [{"internalType": "address", "name": "from", "type": "address"}, {"internalType": "address", "name": "to", "type": "address"}, {"internalType": "uint256", "name": "tokenId", "type": "uint256"}], "name": "transferFrom", "outputs": [], "stateMutability": "nonpayable", "type": "function"}], "devdoc": {"errors": {"ERC721IncorrectOwner(address,uint256,address)": [{"details": "Indicates an error related to the ownership over a particular token. Used in transfers.", "params": {"owner": "Address of the current owner of a token.", "sender": "Address whose tokens are being transferred.", "tokenId": "Identifier number of a token."}}], "ERC721InsufficientApproval(address,uint256)": [{"details": "Indicates a failure with the `operator`’s approval. Used in transfers.", "params": {"operator": "Address that may be allowed to operate on tokens without being their owner.", "tokenId": "Identifier number of a token."}}], "ERC721InvalidApprover(address)": [{"details": "Indicates a failure with the `approver` of a token to be approved. Used in approvals.", "params": {"approver": "Address initiating an approval operation."}}], "ERC721InvalidOperator(address)": [{"details": "Indicates a failure with the `operator` to be approved. Used in approvals.", "params": {"operator": "Address that may be allowed to operate on tokens without being their owner."}}], "ERC721InvalidOwner(address)": [{"details": "Indicates that an address can't be an owner. For example, `address(0)` is a forbidden owner in ERC-20. Used in balance queries.", "params": {"owner": "Address of the current owner of a token."}}], "ERC721InvalidReceiver(address)": [{"details": "Indicates a failure with the token `receiver`. Used in transfers.", "params": {"receiver": "Address to which tokens are being transferred."}}], "ERC721InvalidSender(address)": [{"details": "Indicates a failure with the token `sender`. Used in transfers.", "params": {"sender": "Address whose tokens are being transferred."}}], "ERC721NonexistentToken(uint256)": [{"details": "Indicates a `tokenId` whose `owner` is the zero address.", "params": {"tokenId": "Identifier number of a token."}}]}, "events": {"Approval(address,address,uint256)": {"details": "Emitted when `owner` enables `approved` to manage the `tokenId` token."}, "ApprovalForAll(address,address,bool)": {"details": "Emitted when `owner` enables or disables (`approved`) `operator` to manage all of its assets."}, "Transfer(address,address,uint256)": {"details": "Emitted when `tokenId` token is transferred from `from` to `to`."}}, "kind": "dev", "methods": {"approve(address,uint256)": {"details": "See {IERC721-approve}."}, "balanceOf(address)": {"details": "See {IERC721-balanceOf}."}, "getApproved(uint256)": {"details": "See {IERC721-getApproved}."}, "isApprovedForAll(address,address)": {"details": "See {IERC721-isApprovedForAll}."}, "name()": {"details": "See {IERC721Metadata-name}."}, "ownerOf(uint256)": {"details": "See {IERC721-ownerOf}."}, "safeTransferFrom(address,address,uint256)": {"details": "See {IERC721-safeTransferFrom}."}, "safeTransferFrom(address,address,uint256,bytes)": {"details": "See {IERC721-safeTransferFrom}."}, "setApprovalForAll(address,bool)": {"details": "See {IERC721-setApprovalForAll}."}, "supportsInterface(bytes4)": {"details": "See {IERC165-supportsInterface}."}, "symbol()": {"details": "See {IERC721Metadata-symbol}."}, "tokenURI(uint256)": {"details": "See {IERC721Metadata-tokenURI}."}, "transferFrom(address,address,uint256)": {"details": "See {IERC721-transferFrom}."}}, "version": 1}, "userdoc": {"kind": "user", "methods": {}, "version": 1}}, "settings": {"compilationTarget": {"contracts/nft/NFTi.sol": "NFTi"}, "evmVersion": "shanghai", "libraries": {}, "metadata": {"bytecodeHash": "ipfs"}, "optimizer": {"enabled": false, "runs": 200}, "remappings": []}, "sources": {"@openzeppelin/contracts/interfaces/draft-IERC6093.sol": {"keccak256": "0x880da465c203cec76b10d72dbd87c80f387df4102274f23eea1f9c9b0918792b", "license": "MIT", "urls": ["bzz-raw://399594cd8bb0143bc9e55e0f1d071d0d8c850a394fb7a319d50edd55d9ed822b", "dweb:/ipfs/QmbPZzgtT6LEm9CMqWfagQFwETbV1ztpECBB1DtQHrKiRz"]}, "@openzeppelin/contracts/token/ERC721/ERC721.sol": {"keccak256": "0x39ed367e54765186281efcfe83e47cf0ad62cc879f10e191360712507125f29a", "license": "MIT", "urls": ["bzz-raw://2c5ae6d85bd48cca8d6d2fcec8c63efd86f56f8a5832577a47e403ce0e65cb09", "dweb:/ipfs/QmUtcS8AbRSWhuc61puYet58os8FvSqm329ChoW8wwZXZk"]}, "@openzeppelin/contracts/token/ERC721/IERC721.sol": {"keccak256": "0x5dc63d1c6a12fe1b17793e1745877b2fcbe1964c3edfd0a482fac21ca8f18261", "license": "MIT", "urls": ["bzz-raw://6b7f97c5960a50fd1822cb298551ffc908e37b7893a68d6d08bce18a11cb0f11", "dweb:/ipfs/QmQQvxBytoY1eBt3pRQDmvH2hZ2yjhs12YqVfzGm7KSURq"]}, "@openzeppelin/contracts/token/ERC721/IERC721Receiver.sol": {"keccak256": "0xb5afb8e8eebc4d1c6404df2f5e1e6d2c3d24fd01e5dfc855314951ecfaae462d", "license": "MIT", "urls": ["bzz-raw://78586466c424f076c6a2a551d848cfbe3f7c49e723830807598484a1047b3b34", "dweb:/ipfs/Qmb717ovcFxm7qgNKEShiV6M9SPR3v1qnNpAGH84D6w29p"]}, "@openzeppelin/contracts/token/ERC721/extensions/IERC721Metadata.sol": {"keccak256": "0x37d1aaaa5a2908a09e9dcf56a26ddf762ecf295afb5964695937344fc6802ce1", "license": "MIT", "urls": ["bzz-raw://ed0bfc1b92153c5000e50f4021367b931bbe96372ac6facec3c4961b72053d02", "dweb:/ipfs/Qmbwp8VDerjS5SV1quwHH1oMXxPQ93fzfLVqJ2RCqbowGE"]}, "@openzeppelin/contracts/token/ERC721/utils/ERC721Utils.sol": {"keccak256": "0x40399695922383778f9f540a620bec475a2f8e0f08d41f0005682842e28a9855", "license": "MIT", "urls": ["bzz-raw://746d295e403931aeb9d6065fd5a0871f43ab5459814a60623611e4b6641a09fd", "dweb:/ipfs/QmWrgT8YJrQ9FfD1o3YYArwo57e7MGdpFKuM74qJ4qE34E"]}, "@openzeppelin/contracts/utils/Context.sol": {"keccak256": "0x493033a8d1b176a037b2cc6a04dad01a5c157722049bbecf632ca876224dd4b2", "license": "MIT", "urls": ["bzz-raw://6a708e8a5bdb1011c2c381c9a5cfd8a9a956d7d0a9dc1bd8bcdaf52f76ef2f12", "dweb:/ipfs/Qmax9WHBnVsZP46ZxEMNRQpLQnrdE4dK8LehML1Py8FowF"]}, "@openzeppelin/contracts/utils/Panic.sol": {"keccak256": "0xf7fe324703a64fc51702311dc51562d5cb1497734f074e4f483bfb6717572d7a", "license": "MIT", "urls": ["bzz-raw://c6a5ff4f9fd8649b7ee20800b7fa387d3465bd77cf20c2d1068cd5c98e1ed57a", "dweb:/ipfs/QmVSaVJf9FXFhdYEYeCEfjMVHrxDh5qL4CGkxdMWpQCrqG"]}, "@openzeppelin/contracts/utils/Strings.sol": {"keccak256": "0x44f87e91783e88415bde66f1a63f6c7f0076f2d511548820407d5c95643ac56c", "license": "MIT", "urls": ["bzz-raw://13a51bc2b23827744dcf5bad10c69e72528cf015a6fe48c93632cdb2c0eb1251", "dweb:/ipfs/QmZwPA47Yqgje1qtkdEFEja8ntTahMStYzKf5q3JRnaR7d"]}, "@openzeppelin/contracts/utils/introspection/ERC165.sol": {"keccak256": "0xddce8e17e3d3f9ed818b4f4c4478a8262aab8b11ed322f1bf5ed705bb4bd97fa", "license": "MIT", "urls": ["bzz-raw://8084aa71a4cc7d2980972412a88fe4f114869faea3fefa5436431644eb5c0287", "dweb:/ipfs/Qmbqfs5dRdPvHVKY8kTaeyc65NdqXRQwRK7h9s5UJEhD1p"]}, "@openzeppelin/contracts/utils/introspection/IERC165.sol": {"keccak256": "0x79796192ec90263f21b464d5bc90b777a525971d3de8232be80d9c4f9fb353b8", "license": "MIT", "urls": ["bzz-raw://f6fda447a62815e8064f47eff0dd1cf58d9207ad69b5d32280f8d7ed1d1e4621", "dweb:/ipfs/QmfDRc7pxfaXB2Dh9np5Uf29Na3pQ7tafRS684wd3GLjVL"]}, "@openzeppelin/contracts/utils/math/Math.sol": {"keccak256": "0xa00be322d7db5786750ce0ac7e2f5b633ac30a5ed5fa1ced1e74acfc19acecea", "license": "MIT", "urls": ["bzz-raw://6c84e822f87cbdc4082533b626667b6928715bb2b1e8e7eb96954cebb9e38c8d", "dweb:/ipfs/QmZmy9dgxLTerBAQDuuHqbL6EpgRxddqgv5KmwpXYVbKz1"]}, "@openzeppelin/contracts/utils/math/SafeCast.sol": {"keccak256": "0x195533c86d0ef72bcc06456a4f66a9b941f38eb403739b00f21fd7c1abd1ae54", "license": "MIT", "urls": ["bzz-raw://b1d578337048cad08c1c03041cca5978eff5428aa130c781b271ad9e5566e1f8", "dweb:/ipfs/QmPFKL2r9CBsMwmUqqdcFPfHZB2qcs9g1HDrPxzWSxomvy"]}, "@openzeppelin/contracts/utils/math/SignedMath.sol": {"keccak256": "0xb1970fac7b64e6c09611e6691791e848d5e3fe410fa5899e7df2e0afd77a99e3", "license": "MIT", "urls": ["bzz-raw://db5fbb3dddd8b7047465b62575d96231ba8a2774d37fb4737fbf23340fabbb03", "dweb:/ipfs/QmVUSvooZKEdEdap619tcJjTLcAuH6QBdZqAzWwnAXZAWJ"]}, "contracts/nft/NFTi.sol": {"keccak256": "0x53e07c8488e9f1e64050f2ae5d94d6430df0b38042c3155bae682cb4600774be", "license": "MIT", "urls": ["bzz-raw://5bfb85bad5aa0434c8f64ac389b9a23bbeece0978718d8a3fca10b919ab66e9e", "dweb:/ipfs/QmSq1Jyv3796EirHRZZXdHmSBLDgZJoMDUsjzi1vtjP1vZ"]}}, "version": 1}