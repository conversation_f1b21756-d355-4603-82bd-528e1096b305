{"_format": "hh-sol-artifact-1", "contractName": "INFTm", "sourceName": "contracts/nft/NFTi.sol", "abi": [{"inputs": [{"internalType": "uint256", "name": "nftiTokenId", "type": "uint256"}], "name": "handleNFTiBurn", "outputs": [], "stateMutability": "nonpayable", "type": "function"}, {"inputs": [{"internalType": "bytes32", "name": "role", "type": "bytes32"}, {"internalType": "address", "name": "account", "type": "address"}], "name": "hasRole", "outputs": [{"internalType": "bool", "name": "", "type": "bool"}], "stateMutability": "view", "type": "function"}], "bytecode": "0x", "deployedBytecode": "0x", "linkReferences": {}, "deployedLinkReferences": {}}