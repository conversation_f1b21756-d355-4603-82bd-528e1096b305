{"_format": "hh-sol-artifact-1", "contractName": "SafeMath", "sourceName": "@openzeppelin/contracts/utils/math/SafeMath.sol", "abi": [], "bytecode": "0x60566037600b82828239805160001a607314602a57634e487b7160e01b600052600060045260246000fd5b30600052607381538281f3fe73000000000000000000000000000000000000000030146080604052600080fdfea2646970667358221220164e087ab416e70ed75a5f40b5bce3012c05cb67845009d3361c4790d0e22b5564736f6c63430008140033", "deployedBytecode": "0x73000000000000000000000000000000000000000030146080604052600080fdfea2646970667358221220164e087ab416e70ed75a5f40b5bce3012c05cb67845009d3361c4790d0e22b5564736f6c63430008140033", "linkReferences": {}, "deployedLinkReferences": {}}