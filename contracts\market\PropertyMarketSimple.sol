// SPDX-License-Identifier: MIT
pragma solidity 0.8.20;

import "@openzeppelin/contracts/token/ERC721/IERC721.sol";
import "@openzeppelin/contracts/token/ERC20/IERC20.sol";
import "@openzeppelin/contracts/security/ReentrancyGuard.sol";
import "@openzeppelin/contracts/access/AccessControl.sol";

/// @title PropertyMarketSimple - Simplified version for testing seller confirmation
contract PropertyMarketSimple is ReentrancyGuard, AccessControl {
    
    // ========== Constants ==========
    uint256 public constant PERCENTAGE_BASE = 10000;
    uint256 public constant DEFAULT_CONFIRMATION_PERIOD = 24 hours;
    
    // ========== Data Structures ==========
    enum PropertyStatus { LISTED, SOLD, PENDING_SELLER_CONFIRMATION }
    
    struct PropertyListing {
        uint256 tokenId;
        address seller;
        uint256 price;
        address paymentToken;
        PropertyStatus status;
        uint256 listTimestamp;
        uint256 confirmationPeriod;
    }
    
    struct PendingPurchase {
        uint256 tokenId;
        address buyer;
        uint256 offerPrice;
        address paymentToken;
        uint256 purchaseTimestamp;
        uint256 confirmationDeadline;
        bool isActive;
    }
    
    // ========== State Variables ==========
    IERC721 public immutable nftiContract;
    
    mapping(uint256 => PropertyListing) public listings;
    mapping(uint256 => PendingPurchase) public pendingPurchases;
    mapping(address => bool) public allowedPaymentTokens;
    
    // ========== Events ==========
    event NewListing(uint256 indexed tokenId, address indexed seller, uint256 price, address paymentToken);
    event PropertySold(uint256 indexed tokenId, address indexed buyer, uint256 price, address paymentToken);
    
    event PurchaseRequested(
        uint256 indexed tokenId,
        address indexed buyer,
        uint256 offerPrice,
        address paymentToken,
        uint256 confirmationDeadline
    );
    
    event PurchaseConfirmed(
        uint256 indexed tokenId,
        address indexed seller,
        address indexed buyer,
        uint256 finalPrice,
        address paymentToken
    );
    
    event PurchaseRejected(
        uint256 indexed tokenId,
        address indexed seller,
        address indexed buyer,
        uint256 offerPrice,
        address paymentToken
    );
    
    event PurchaseExpired(
        uint256 indexed tokenId,
        address indexed buyer,
        uint256 offerPrice,
        address paymentToken
    );
    
    // ========== Constructor ==========
    constructor(address _nftiAddress) {
        require(_nftiAddress != address(0), "Invalid NFT address");
        
        nftiContract = IERC721(_nftiAddress);
        allowedPaymentTokens[address(0)] = true; // Allow ETH
        
        _grantRole(DEFAULT_ADMIN_ROLE, msg.sender);
    }
    
    // ========== Core Functions ==========
    
    /// @notice Lists a property NFT for sale
    function listProperty(
        uint256 tokenId,
        uint256 price,
        address paymentToken
    ) external nonReentrant {
        _listPropertyWithConfirmation(tokenId, price, paymentToken, 0);
    }
    
    /// @notice Lists a property NFT for sale with seller confirmation period
    function listPropertyWithConfirmation(
        uint256 tokenId,
        uint256 price,
        address paymentToken,
        uint256 confirmationPeriod
    ) external nonReentrant {
        require(confirmationPeriod <= 7 days, "Confirmation period too long");
        _listPropertyWithConfirmation(tokenId, price, paymentToken, confirmationPeriod);
    }
    
    /// @notice Internal function to handle property listing
    function _listPropertyWithConfirmation(
        uint256 tokenId,
        uint256 price,
        address paymentToken,
        uint256 confirmationPeriod
    ) internal {
        require(nftiContract.ownerOf(tokenId) == msg.sender, "Not the owner");
        require(price > 0, "Invalid price");
        require(allowedPaymentTokens[paymentToken], "Token not allowed");
        require(listings[tokenId].seller == address(0) || listings[tokenId].status != PropertyStatus.LISTED, "Already listed");

        listings[tokenId] = PropertyListing({
            tokenId: tokenId,
            seller: msg.sender,
            price: price,
            paymentToken: paymentToken,
            status: PropertyStatus.LISTED,
            listTimestamp: block.timestamp,
            confirmationPeriod: confirmationPeriod
        });

        emit NewListing(tokenId, msg.sender, price, paymentToken);
    }

    /// @notice Purchases a listed property
    function purchaseProperty(uint256 tokenId, uint256 offerPrice) external payable nonReentrant {
        PropertyListing storage listing = listings[tokenId];
        require(listing.status == PropertyStatus.LISTED, "Not listed");
        require(offerPrice >= listing.price, "Insufficient offer");

        // Check if seller confirmation is required
        if (listing.confirmationPeriod > 0) {
            // Case requiring seller confirmation
            _createPendingPurchase(tokenId, offerPrice, listing.paymentToken);
        } else {
            // Case for immediate transaction
            _completePurchase(tokenId, offerPrice, listing.paymentToken);
        }
    }
    
    /// @notice Create a pending purchase order awaiting confirmation
    function _createPendingPurchase(uint256 tokenId, uint256 actualPrice, address paymentToken) internal {
        PropertyListing storage listing = listings[tokenId];
        
        // Lock buyer's funds
        if (paymentToken == address(0)) {
            require(msg.value >= actualPrice, "Insufficient ETH sent");
        } else {
            IERC20 token = IERC20(paymentToken);
            require(token.transferFrom(msg.sender, address(this), actualPrice), "Token transfer failed");
        }
        
        // Set pending confirmation status
        listing.status = PropertyStatus.PENDING_SELLER_CONFIRMATION;
        uint256 deadline = block.timestamp + listing.confirmationPeriod;
        
        pendingPurchases[tokenId] = PendingPurchase({
            tokenId: tokenId,
            buyer: msg.sender,
            offerPrice: actualPrice,
            paymentToken: paymentToken,
            purchaseTimestamp: block.timestamp,
            confirmationDeadline: deadline,
            isActive: true
        });
        
        emit PurchaseRequested(tokenId, msg.sender, actualPrice, paymentToken, deadline);
    }
    
    /// @notice Complete the purchase transaction
    function _completePurchase(uint256 tokenId, uint256 actualPrice, address paymentToken) internal {
        PropertyListing storage listing = listings[tokenId];
        
        listing.status = PropertyStatus.SOLD;
        
        // Transfer payment
        if (paymentToken == address(0)) {
            require(msg.value >= actualPrice, "Insufficient ETH");
            (bool success, ) = payable(listing.seller).call{value: actualPrice}("");
            require(success, "ETH transfer failed");
        } else {
            IERC20 token = IERC20(paymentToken);
            require(token.transferFrom(msg.sender, listing.seller, actualPrice), "Token transfer failed");
        }
        
        // Transfer NFT
        nftiContract.safeTransferFrom(listing.seller, msg.sender, tokenId, "");
        
        emit PropertySold(tokenId, msg.sender, actualPrice, paymentToken);
    }

    /// @notice Seller confirms the purchase order
    function confirmPurchase(uint256 tokenId) external nonReentrant {
        PropertyListing storage listing = listings[tokenId];
        PendingPurchase storage purchase = pendingPurchases[tokenId];
        
        require(listing.status == PropertyStatus.PENDING_SELLER_CONFIRMATION, "No pending purchase");
        require(purchase.isActive, "Purchase not active");
        require(nftiContract.ownerOf(tokenId) == msg.sender, "Not the seller");
        require(block.timestamp <= purchase.confirmationDeadline, "Confirmation period expired");
        
        // Complete the transaction
        purchase.isActive = false;
        listing.status = PropertyStatus.SOLD;
        
        // Transfer payment to seller
        if (purchase.paymentToken == address(0)) {
            (bool success, ) = payable(listing.seller).call{value: purchase.offerPrice}("");
            require(success, "ETH transfer failed");
        } else {
            IERC20 token = IERC20(purchase.paymentToken);
            require(token.transfer(listing.seller, purchase.offerPrice), "Token transfer failed");
        }
        
        // Transfer NFT to buyer
        nftiContract.safeTransferFrom(listing.seller, purchase.buyer, tokenId, "");
        
        emit PurchaseConfirmed(tokenId, msg.sender, purchase.buyer, purchase.offerPrice, purchase.paymentToken);
        emit PropertySold(tokenId, purchase.buyer, purchase.offerPrice, purchase.paymentToken);
    }

    /// @notice Seller rejects the purchase order
    function rejectPurchase(uint256 tokenId) external nonReentrant {
        PropertyListing storage listing = listings[tokenId];
        PendingPurchase storage purchase = pendingPurchases[tokenId];
        
        require(listing.status == PropertyStatus.PENDING_SELLER_CONFIRMATION, "No pending purchase");
        require(purchase.isActive, "Purchase not active");
        require(nftiContract.ownerOf(tokenId) == msg.sender, "Not the seller");
        require(block.timestamp <= purchase.confirmationDeadline, "Confirmation period expired");
        
        // Refund to buyer
        _refundPendingPurchase(tokenId);
        
        // Restore listing status
        purchase.isActive = false;
        listing.status = PropertyStatus.LISTED;
        
        emit PurchaseRejected(tokenId, msg.sender, purchase.buyer, purchase.offerPrice, purchase.paymentToken);
    }

    /// @notice Buyer or anyone can cancel the order after confirmation period expires
    function cancelExpiredPurchase(uint256 tokenId) external nonReentrant {
        PropertyListing storage listing = listings[tokenId];
        PendingPurchase storage purchase = pendingPurchases[tokenId];
        
        require(listing.status == PropertyStatus.PENDING_SELLER_CONFIRMATION, "No pending purchase");
        require(purchase.isActive, "Purchase not active");
        require(block.timestamp > purchase.confirmationDeadline, "Confirmation period not expired");
        
        // Refund to buyer
        _refundPendingPurchase(tokenId);
        
        // Restore listing status
        purchase.isActive = false;
        listing.status = PropertyStatus.LISTED;
        
        emit PurchaseExpired(tokenId, purchase.buyer, purchase.offerPrice, purchase.paymentToken);
    }
    
    /// @notice Refund pending purchase order
    function _refundPendingPurchase(uint256 tokenId) internal {
        PendingPurchase storage purchase = pendingPurchases[tokenId];
        
        if (purchase.paymentToken == address(0)) {
            // Refund ETH
            (bool success, ) = payable(purchase.buyer).call{value: purchase.offerPrice}("");
            require(success, "ETH refund failed");
        } else {
            // Refund ERC20 tokens
            IERC20 token = IERC20(purchase.paymentToken);
            require(token.transfer(purchase.buyer, purchase.offerPrice), "Token refund failed");
        }
    }

    // ========== View Functions ==========
    
    /// @notice Get listing details
    function getListingDetails(uint256 tokenId)
        external
        view
        returns (
            address seller,
            uint256 price,
            address paymentToken,
            PropertyStatus status,
            uint256 listTimestamp,
            uint256 confirmationPeriod
        )
    {
        PropertyListing storage listing = listings[tokenId];
        return (
            listing.seller,
            listing.price,
            listing.paymentToken,
            listing.status,
            listing.listTimestamp,
            listing.confirmationPeriod
        );
    }
    
    /// @notice Get pending purchase order details
    function getPendingPurchaseDetails(uint256 tokenId)
        external
        view
        returns (
            address buyer,
            uint256 offerPrice,
            address paymentToken,
            uint256 purchaseTimestamp,
            uint256 confirmationDeadline,
            bool isActive,
            bool isExpired
        )
    {
        PendingPurchase storage purchase = pendingPurchases[tokenId];
        return (
            purchase.buyer,
            purchase.offerPrice,
            purchase.paymentToken,
            purchase.purchaseTimestamp,
            purchase.confirmationDeadline,
            purchase.isActive,
            block.timestamp > purchase.confirmationDeadline
        );
    }
    
    /// @notice Check if property requires seller confirmation
    function requiresSellerConfirmation(uint256 tokenId) external view returns (bool) {
        return listings[tokenId].confirmationPeriod > 0;
    }
    
    // ========== Admin Functions ==========
    
    /// @notice Add or remove allowed payment tokens
    function setPaymentTokenAllowed(address token, bool allowed) external onlyRole(DEFAULT_ADMIN_ROLE) {
        allowedPaymentTokens[token] = allowed;
    }
}
