{"deploy": {"VM:-": {"linkReferences": {}, "autoDeployLib": true}, "main:1": {"linkReferences": {}, "autoDeployLib": true}, "ropsten:3": {"linkReferences": {}, "autoDeployLib": true}, "rinkeby:4": {"linkReferences": {}, "autoDeployLib": true}, "kovan:42": {"linkReferences": {}, "autoDeployLib": true}, "goerli:5": {"linkReferences": {}, "autoDeployLib": true}, "Custom": {"linkReferences": {}, "autoDeployLib": true}}, "data": {"bytecode": {"functionDebugData": {}, "generatedSources": [], "linkReferences": {}, "object": "", "opcodes": "", "sourceMap": ""}, "deployedBytecode": {"functionDebugData": {}, "generatedSources": [], "immutableReferences": {}, "linkReferences": {}, "object": "", "opcodes": "", "sourceMap": ""}, "gasEstimates": null, "methodIdentifiers": {"isAdmin(address)": "24d7806c", "isLegalAuthority(address)": "0d0606fa", "operator()": "570ca735"}}, "abi": [{"inputs": [{"internalType": "address", "name": "account", "type": "address"}], "name": "isAdmin", "outputs": [{"internalType": "bool", "name": "", "type": "bool"}], "stateMutability": "view", "type": "function"}, {"inputs": [{"internalType": "address", "name": "account", "type": "address"}], "name": "isLegalAuthority", "outputs": [{"internalType": "bool", "name": "", "type": "bool"}], "stateMutability": "view", "type": "function"}, {"inputs": [], "name": "operator", "outputs": [{"internalType": "address", "name": "", "type": "address"}], "stateMutability": "view", "type": "function"}]}