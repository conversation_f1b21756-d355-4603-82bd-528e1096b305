{"_format": "hh-sol-artifact-1", "contractName": "PaymentProcessorV2", "sourceName": "contracts/libraries/PaymentProcessorV2.sol", "abi": [{"anonymous": false, "inputs": [{"indexed": true, "internalType": "address", "name": "seller", "type": "address"}, {"indexed": true, "internalType": "address", "name": "buyer", "type": "address"}, {"indexed": false, "internalType": "uint256", "name": "amount", "type": "uint256"}, {"indexed": false, "internalType": "uint256", "name": "fees", "type": "uint256"}, {"indexed": false, "internalType": "address", "name": "paymentToken", "type": "address"}], "name": "PaymentProcessed", "type": "event"}, {"anonymous": false, "inputs": [{"indexed": true, "internalType": "address", "name": "user", "type": "address"}, {"indexed": false, "internalType": "uint256", "name": "amount", "type": "uint256"}], "name": "RefundStored", "type": "event"}, {"anonymous": false, "inputs": [{"indexed": true, "internalType": "address", "name": "user", "type": "address"}, {"indexed": false, "internalType": "uint256", "name": "amount", "type": "uint256"}], "name": "RefundWithdrawn", "type": "event"}, {"inputs": [{"internalType": "uint256", "name": "amount", "type": "uint256"}, {"components": [{"internalType": "uint256", "name": "baseFee", "type": "uint256"}, {"internalType": "address", "name": "feeCollector", "type": "address"}, {"internalType": "uint256", "name": "percentageBase", "type": "uint256"}], "internalType": "struct PaymentProcessorV2.PaymentConfig", "name": "config", "type": "tuple"}], "name": "calculateFees", "outputs": [{"internalType": "uint256", "name": "fees", "type": "uint256"}], "stateMutability": "pure", "type": "function"}, {"inputs": [{"internalType": "address", "name": "user", "type": "address"}], "name": "getPendingRefund", "outputs": [{"internalType": "uint256", "name": "amount", "type": "uint256"}], "stateMutability": "view", "type": "function"}, {"inputs": [{"internalType": "address", "name": "", "type": "address"}], "name": "pendingRefunds", "outputs": [{"internalType": "uint256", "name": "", "type": "uint256"}], "stateMutability": "view", "type": "function"}, {"inputs": [{"components": [{"internalType": "uint256", "name": "baseFee", "type": "uint256"}, {"internalType": "address", "name": "feeCollector", "type": "address"}, {"internalType": "uint256", "name": "percentageBase", "type": "uint256"}], "internalType": "struct PaymentProcessorV2.PaymentConfig", "name": "config", "type": "tuple"}, {"internalType": "address payable", "name": "seller", "type": "address"}, {"internalType": "address", "name": "buyer", "type": "address"}, {"internalType": "uint256", "name": "netValue", "type": "uint256"}, {"internalType": "uint256", "name": "fees", "type": "uint256"}], "name": "processETHPayment", "outputs": [], "stateMutability": "payable", "type": "function"}, {"inputs": [{"internalType": "address", "name": "paymentToken", "type": "address"}, {"internalType": "address", "name": "buyer", "type": "address"}, {"internalType": "address", "name": "seller", "type": "address"}, {"internalType": "address", "name": "feeCollector", "type": "address"}, {"internalType": "uint256", "name": "netValue", "type": "uint256"}, {"internalType": "uint256", "name": "fees", "type": "uint256"}], "name": "processTokenPayment", "outputs": [], "stateMutability": "nonpayable", "type": "function"}, {"inputs": [], "name": "withdrawPendingRefund", "outputs": [], "stateMutability": "nonpayable", "type": "function"}], "bytecode": "0x608060405234801561001057600080fd5b506001600055610ade806100256000396000f3fe6080604052600436106100555760003560e01c80634ca6fd2c1461005a57806356c3d69b146100715780637f69bce71461008457806396230da4146100b6578063b613b114146100ec578063cfbb36a614610119575b600080fd5b34801561006657600080fd5b5061006f610139565b005b61006f61007f366004610842565b61027c565b34801561009057600080fd5b506100a461009f36600461089c565b6104f8565b60405190815260200160405180910390f35b3480156100c257600080fd5b506100a46100d13660046108c9565b6001600160a01b031660009081526001602052604090205490565b3480156100f857600080fd5b506100a46101073660046108c9565b60016020526000908152604090205481565b34801561012557600080fd5b5061006f6101343660046108ed565b610520565b610141610761565b33600090815260016020526040902054806101975760405162461bcd60e51b8152602060048201526011602482015270139bc81c195b991a5b99c81c99599d5b99607a1b60448201526064015b60405180910390fd5b336000818152600160205260408082208290555190919083908381818185875af1925050503d80600081146101e8576040519150601f19603f3d011682016040523d82523d6000602084013e6101ed565b606091505b50509050806102395760405162461bcd60e51b81526020600482015260186024820152771499599d5b99081dda5d1a191c985dd85b0819985a5b195960421b604482015260640161018e565b60405182815233907f3d97f39b86d061200a7834082f5926e58ec10fd85a9d6930f497729d5e6cc35c9060200160405180910390a2505061027a6001600055565b565b610284610761565b60006102908284610971565b9050803410156102d95760405162461bcd60e51b8152602060048201526014602482015273125b9cdd59999a58da595b9d081c185e5b595b9d60621b604482015260640161018e565b6000856001600160a01b03168460405160006040518083038185875af1925050503d8060008114610326576040519150601f19603f3d011682016040523d82523d6000602084013e61032b565b606091505b505090508061034c5760405162461bcd60e51b815260040161018e90610984565b600087602001516001600160a01b03168460405160006040518083038185875af1925050503d806000811461039d576040519150601f19603f3d011682016040523d82523d6000602084013e6103a2565b606091505b50509050806103c35760405162461bcd60e51b815260040161018e906109b6565b60006103cf84346109e2565b905080156104a5576000876001600160a01b03168261271090604051600060405180830381858888f193505050503d8060008114610429576040519150601f19603f3d011682016040523d82523d6000602084013e61042e565b606091505b50509050806104a3576001600160a01b0388166000908152600160205260408120805484929061045f908490610971565b90915550506040518281526001600160a01b038916907f783ab5f5d03a173a5dcc2942c2c25ee3b33a9c44ecebc6b844ae1a286f02977e9060200160405180910390a25b505b866001600160a01b0316886001600160a01b0316600080516020610a89833981519152888860006040516104db939291906109f5565b60405180910390a3505050506104f16001600055565b5050505050565b604081015181516000919061050d9085610a14565b6105179190610a2b565b90505b92915050565b610528610761565b8560006105358385610971565b6040516323b872dd60e01b81526001600160a01b03898116600483015230602483015260448201839052919250908316906323b872dd906064016020604051808303816000875af115801561058e573d6000803e3d6000fd5b505050506040513d601f19601f820116820180604052508101906105b29190610a4d565b6105f65760405162461bcd60e51b8152602060048201526015602482015274151bdad95b881d1c985b9cd9995c8819985a5b1959605a1b604482015260640161018e565b60405163a9059cbb60e01b81526001600160a01b0383169063a9059cbb906106249089908890600401610a6f565b6020604051808303816000875af1158015610643573d6000803e3d6000fd5b505050506040513d601f19601f820116820180604052508101906106679190610a4d565b6106835760405162461bcd60e51b815260040161018e90610984565b60405163a9059cbb60e01b81526001600160a01b0383169063a9059cbb906106b19088908790600401610a6f565b6020604051808303816000875af11580156106d0573d6000803e3d6000fd5b505050506040513d601f19601f820116820180604052508101906106f49190610a4d565b6107105760405162461bcd60e51b815260040161018e906109b6565b866001600160a01b0316866001600160a01b0316600080516020610a8983398151915286868c604051610745939291906109f5565b60405180910390a350506107596001600055565b505050505050565b6002600054036107b35760405162461bcd60e51b815260206004820152601f60248201527f5265656e7472616e637947756172643a207265656e7472616e742063616c6c00604482015260640161018e565b6002600055565b6001600160a01b03811681146107cf57600080fd5b50565b6000606082840312156107e457600080fd5b604051606081016001600160401b038111828210171561081457634e487b7160e01b600052604160045260246000fd5b60405282358152905080602083013561082c816107ba565b6020820152604092830135920191909152919050565b600080600080600060e0868803121561085a57600080fd5b61086487876107d2565b94506060860135610874816107ba565b93506080860135610884816107ba565b9497939650939460a0810135945060c0013592915050565b600080608083850312156108af57600080fd5b823591506108c084602085016107d2565b90509250929050565b6000602082840312156108db57600080fd5b81356108e6816107ba565b9392505050565b60008060008060008060c0878903121561090657600080fd5b8635610911816107ba565b95506020870135610921816107ba565b94506040870135610931816107ba565b93506060870135610941816107ba565b9598949750929560808101359460a0909101359350915050565b634e487b7160e01b600052601160045260246000fd5b8082018082111561051a5761051a61095b565b60208082526018908201527714185e5b595b9d081d1bc81cd95b1b195c8819985a5b195960421b604082015260600190565b602080825260129082015271119959481c185e5b595b9d0819985a5b195960721b604082015260600190565b8181038181111561051a5761051a61095b565b92835260208301919091526001600160a01b0316604082015260600190565b808202811582820484141761051a5761051a61095b565b600082610a4857634e487b7160e01b600052601260045260246000fd5b500490565b600060208284031215610a5f57600080fd5b815180151581146108e657600080fd5b6001600160a01b0392909216825260208201526040019056fea496c08b103292578a04c9879f2740638414c311180dffa9d1436d3c97119923a2646970667358221220a1f0e9c7c185986cdc2e402d42ae5a1cc32035d90b9e4c3bfad81d853866025d64736f6c63430008140033", "deployedBytecode": "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", "linkReferences": {}, "deployedLinkReferences": {}}