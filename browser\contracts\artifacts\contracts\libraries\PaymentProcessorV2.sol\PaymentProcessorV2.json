{"_format": "hh-sol-artifact-1", "contractName": "PaymentProcessorV2", "sourceName": "contracts/libraries/PaymentProcessorV2.sol", "abi": [{"anonymous": false, "inputs": [{"indexed": true, "internalType": "address", "name": "seller", "type": "address"}, {"indexed": true, "internalType": "address", "name": "buyer", "type": "address"}, {"indexed": false, "internalType": "uint256", "name": "amount", "type": "uint256"}, {"indexed": false, "internalType": "uint256", "name": "fees", "type": "uint256"}, {"indexed": false, "internalType": "address", "name": "paymentToken", "type": "address"}], "name": "PaymentProcessed", "type": "event"}, {"anonymous": false, "inputs": [{"indexed": true, "internalType": "address", "name": "user", "type": "address"}, {"indexed": false, "internalType": "uint256", "name": "amount", "type": "uint256"}], "name": "RefundStored", "type": "event"}, {"anonymous": false, "inputs": [{"indexed": true, "internalType": "address", "name": "user", "type": "address"}, {"indexed": false, "internalType": "uint256", "name": "amount", "type": "uint256"}], "name": "RefundWithdrawn", "type": "event"}, {"inputs": [{"internalType": "uint256", "name": "amount", "type": "uint256"}, {"components": [{"internalType": "uint256", "name": "baseFee", "type": "uint256"}, {"internalType": "address", "name": "feeCollector", "type": "address"}, {"internalType": "uint256", "name": "percentageBase", "type": "uint256"}], "internalType": "struct PaymentProcessorV2.PaymentConfig", "name": "config", "type": "tuple"}], "name": "calculateFees", "outputs": [{"internalType": "uint256", "name": "fees", "type": "uint256"}], "stateMutability": "pure", "type": "function"}, {"inputs": [{"internalType": "address", "name": "user", "type": "address"}], "name": "getPendingRefund", "outputs": [{"internalType": "uint256", "name": "amount", "type": "uint256"}], "stateMutability": "view", "type": "function"}, {"inputs": [{"internalType": "address", "name": "", "type": "address"}], "name": "pendingRefunds", "outputs": [{"internalType": "uint256", "name": "", "type": "uint256"}], "stateMutability": "view", "type": "function"}, {"inputs": [{"components": [{"internalType": "uint256", "name": "baseFee", "type": "uint256"}, {"internalType": "address", "name": "feeCollector", "type": "address"}, {"internalType": "uint256", "name": "percentageBase", "type": "uint256"}], "internalType": "struct PaymentProcessorV2.PaymentConfig", "name": "config", "type": "tuple"}, {"internalType": "address payable", "name": "seller", "type": "address"}, {"internalType": "address", "name": "buyer", "type": "address"}, {"internalType": "uint256", "name": "netValue", "type": "uint256"}, {"internalType": "uint256", "name": "fees", "type": "uint256"}], "name": "processETHPayment", "outputs": [], "stateMutability": "payable", "type": "function"}, {"inputs": [{"internalType": "address", "name": "paymentToken", "type": "address"}, {"internalType": "address", "name": "buyer", "type": "address"}, {"internalType": "address", "name": "seller", "type": "address"}, {"internalType": "address", "name": "feeCollector", "type": "address"}, {"internalType": "uint256", "name": "netValue", "type": "uint256"}, {"internalType": "uint256", "name": "fees", "type": "uint256"}], "name": "processTokenPayment", "outputs": [], "stateMutability": "nonpayable", "type": "function"}, {"inputs": [], "name": "withdrawPendingRefund", "outputs": [], "stateMutability": "nonpayable", "type": "function"}], "bytecode": "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", "deployedBytecode": "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", "linkReferences": {}, "deployedLinkReferences": {}}