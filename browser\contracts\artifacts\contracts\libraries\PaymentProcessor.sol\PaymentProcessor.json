{"_format": "hh-sol-artifact-1", "contractName": "PaymentProcessor", "sourceName": "contracts/libraries/PaymentProcessor.sol", "abi": [{"anonymous": false, "inputs": [{"indexed": true, "internalType": "address", "name": "seller", "type": "address"}, {"indexed": true, "internalType": "address", "name": "buyer", "type": "address"}, {"indexed": false, "internalType": "uint256", "name": "amount", "type": "uint256"}, {"indexed": false, "internalType": "uint256", "name": "fees", "type": "uint256"}, {"indexed": false, "internalType": "address", "name": "paymentToken", "type": "address"}], "name": "PaymentProcessed", "type": "event"}], "bytecode": "0x60566037600b82828239805160001a607314602a57634e487b7160e01b600052600060045260246000fd5b30600052607381538281f3fe73000000000000000000000000000000000000000030146080604052600080fdfea26469706673582212202eae7e67a42ef334392e53f840337d6b02d4ef27d02c75eee930ab78011b3e3c64736f6c63430008140033", "deployedBytecode": "0x73000000000000000000000000000000000000000030146080604052600080fdfea26469706673582212202eae7e67a42ef334392e53f840337d6b02d4ef27d02c75eee930ab78011b3e3c64736f6c63430008140033", "linkReferences": {}, "deployedLinkReferences": {}}