{"_format": "hh-sol-artifact-1", "contractName": "Storage", "sourceName": "contracts/1_Storage.sol", "abi": [{"inputs": [], "name": "retrieve", "outputs": [{"internalType": "uint256", "name": "", "type": "uint256"}], "stateMutability": "view", "type": "function"}, {"inputs": [{"internalType": "uint256", "name": "num", "type": "uint256"}], "name": "store", "outputs": [], "stateMutability": "nonpayable", "type": "function"}], "bytecode": "0x6080604052348015600f57600080fd5b5060ac8061001e6000396000f3fe6080604052348015600f57600080fd5b506004361060325760003560e01c80632e64cec11460375780636057361d14604c575b600080fd5b60005460405190815260200160405180910390f35b605c6057366004605e565b600055565b005b600060208284031215606f57600080fd5b503591905056fea26469706673582212200d44e190663c9e93ed738aa1979a17c00c9e9cc04f78cad889982763c35d496264736f6c63430008140033", "deployedBytecode": "0x6080604052348015600f57600080fd5b506004361060325760003560e01c80632e64cec11460375780636057361d14604c575b600080fd5b60005460405190815260200160405180910390f35b605c6057366004605e565b600055565b005b600060208284031215606f57600080fd5b503591905056fea26469706673582212200d44e190663c9e93ed738aa1979a17c00c9e9cc04f78cad889982763c35d496264736f6c63430008140033", "linkReferences": {}, "deployedLinkReferences": {}}